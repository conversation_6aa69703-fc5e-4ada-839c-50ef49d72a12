import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

interface GenerateMetadataProps {
  locale: string;
  namespace?: string;
  path?: string;
  title?: string;
  description?: string;
}

export async function generatePageMetadata({
  locale,
  namespace = 'metadata',
  path = '',
  title,
  description
}: GenerateMetadataProps): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace });
  
  // 获取当前域名，在生产环境中应该是实际域名
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  const currentUrl = `${baseUrl}/${locale}${path}`;
  
  // 生成所有语言版本的 URL
  const zhUrl = `${baseUrl}/zh${path}`;
  const enUrl = `${baseUrl}/en${path}`;
  
  const pageTitle = title || t('title');
  const pageDescription = description || t('description');
  
  return {
    title: pageTitle,
    description: pageDescription,
    keywords: t('keywords'),
    authors: [{ name: "AnyTool" }],
    robots: "index, follow",
    alternates: {
      canonical: currentUrl,
      languages: {
        'zh-CN': zhUrl,
        'zh': zhUrl,
        'en-US': enUrl,
        'en': enUrl,
        'x-default': zhUrl, // 默认语言设为中文
      }
    },
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: currentUrl,
      siteName: 'AnyTool',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/og-image.png`,
          width: 1200,
          height: 630,
          alt: pageTitle,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      images: [`${baseUrl}/og-image.png`],
    }
  };
}

// 为工具页面生成元数据
export async function generateToolMetadata({
  locale,
  toolName,
  path
}: {
  locale: string;
  toolName: string;
  path: string;
}): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: `tools.${toolName}` });

  const title = `${t('name')} - AnyTool`;
  const description = t('description');

  // 获取当前域名
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  const currentUrl = `${baseUrl}/${locale}${path}`;

  // 生成所有语言版本的 URL
  const zhUrl = `${baseUrl}/zh${path}`;
  const enUrl = `${baseUrl}/en${path}`;

  return {
    title,
    description,
    keywords: t('keywords', { defaultValue: '' }),
    authors: [{ name: "AnyTool" }],
    robots: "index, follow",
    alternates: {
      canonical: currentUrl,
      languages: {
        'zh-CN': zhUrl,
        'zh': zhUrl,
        'en-US': enUrl,
        'en': enUrl,
        'x-default': zhUrl,
      }
    },
    openGraph: {
      title,
      description,
      url: currentUrl,
      siteName: 'AnyTool',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/og-image.png`,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}/og-image.png`],
    }
  };
}
