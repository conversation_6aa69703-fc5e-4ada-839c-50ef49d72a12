# 新工具开发模板

## 快速创建新工具的模板文件

### 1. 服务端页面模板 (`page.tsx`)

```typescript
import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import ToolNameClient from './ToolNameClient';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: 'toolName',           // 替换为实际的翻译键名
    path: '/tools/tool-name'        // 替换为实际的URL路径
  });
}

export default function ToolNamePage() {
  return <ToolNameClient />;
}
```

### 2. 客户端组件模板 (`ToolNameClient.tsx`)

```typescript
"use client";

import { useState, useCallback, useRef } from "react";
import { useTranslations } from "next-intl";
import { Upload, Download, Settings, Info, Loader2 } from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// 定义数据类型
interface ProcessedFile {
  original: File;
  processed: Blob;
  url: string;
  // 添加其他需要的属性
}

export default function ToolNameClient() {
  const t = useTranslations('tools.toolName');
  
  // 状态管理
  const [files, setFiles] = useState<ProcessedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 文件选择处理
  const handleFileSelect = useCallback(async (selectedFiles: FileList) => {
    if (!selectedFiles.length) return;
    
    setIsProcessing(true);
    setError(null);
    
    try {
      const processedFiles: ProcessedFile[] = [];
      
      for (const file of Array.from(selectedFiles)) {
        // 验证文件
        validateFile(file);
        
        // 处理文件
        const processedBlob = await processFile(file);
        const url = URL.createObjectURL(processedBlob);
        
        processedFiles.push({
          original: file,
          processed: processedBlob,
          url
        });
      }
      
      setFiles(prev => [...prev, ...processedFiles]);
    } catch (err) {
      setError(err instanceof Error ? err.message : '处理失败');
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // 文件验证
  const validateFile = (file: File) => {
    const allowedTypes = ['image/jpeg', 'image/png']; // 根据需要修改
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件类型');
    }
    
    if (file.size > maxSize) {
      throw new Error('文件过大，请选择小于10MB的文件');
    }
  };

  // 文件处理核心逻辑
  const processFile = async (file: File): Promise<Blob> => {
    // TODO: 实现具体的文件处理逻辑
    return new Promise((resolve, reject) => {
      // 示例：简单返回原文件
      resolve(file);
    });
  };

  // 下载单个文件
  const downloadFile = useCallback((processedFile: ProcessedFile, filename?: string) => {
    const link = document.createElement('a');
    link.href = processedFile.url;
    link.download = filename || `processed_${processedFile.original.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  // 下载所有文件
  const downloadAll = useCallback(async () => {
    if (files.length === 0) return;
    
    // 如果只有一个文件，直接下载
    if (files.length === 1) {
      downloadFile(files[0]);
      return;
    }
    
    // 多个文件打包下载（需要引入 jszip）
    // const JSZip = (await import('jszip')).default;
    // const zip = new JSZip();
    // 
    // files.forEach((file, index) => {
    //   zip.file(`processed_${index + 1}_${file.original.name}`, file.processed);
    // });
    // 
    // const zipBlob = await zip.generateAsync({ type: 'blob' });
    // const url = URL.createObjectURL(zipBlob);
    // const link = document.createElement('a');
    // link.href = url;
    // link.download = 'processed_files.zip';
    // link.click();
    // URL.revokeObjectURL(url);
  }, [files, downloadFile]);

  // 清空结果
  const clearResults = useCallback(() => {
    files.forEach(file => URL.revokeObjectURL(file.url));
    setFiles([]);
    setError(null);
  }, [files]);

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles);
    }
  }, [handleFileSelect]);

  // 清理内存
  React.useEffect(() => {
    return () => {
      files.forEach(file => URL.revokeObjectURL(file.url));
    };
  }, [files]);

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />
      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>

          {/* 设置面板（可选） */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                {t('settings.title')}
              </CardTitle>
              <CardDescription>{t('settings.description')}</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 添加设置选项 */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="setting1">{t('settings.option1')}</Label>
                  <Input id="setting1" placeholder={t('settings.placeholder1')} />
                </div>
                <div>
                  <Label htmlFor="setting2">{t('settings.option2')}</Label>
                  <Input id="setting2" placeholder={t('settings.placeholder2')} />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 文件上传区域 */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">{t('upload.title')}</h3>
                <p className="text-muted-foreground mb-4">{t('upload.description')}</p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {t('processing')}
                    </>
                  ) : (
                    t('upload.button')
                  )}
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*" // 根据需要修改
                  onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {/* 错误显示 */}
          {error && (
            <Card className="mb-6 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <p className="text-red-600">{error}</p>
              </CardContent>
            </Card>
          )}

          {/* 结果显示 */}
          {files.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    {t('result.title')} ({files.length} {t('result.files')})
                  </span>
                  <div className="flex gap-2">
                    <Button onClick={downloadAll} size="sm">
                      {t('result.downloadAll')}
                    </Button>
                    <Button onClick={clearResults} variant="outline" size="sm">
                      {t('result.clear')}
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{file.original.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {(file.original.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                      <Button
                        onClick={() => downloadFile(file)}
                        size="sm"
                        variant="outline"
                      >
                        {t('result.download')}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
```

### 3. 翻译文件模板

#### 中文 (`messages/zh.json`)
```json
{
  "tools": {
    "toolName": {
      "name": "工具名称",
      "description": "工具描述",
      "title": "工具标题",
      "subtitle": "工具副标题",
      "settings": {
        "title": "设置",
        "description": "配置处理参数",
        "option1": "选项1",
        "option2": "选项2",
        "placeholder1": "输入选项1...",
        "placeholder2": "输入选项2..."
      },
      "upload": {
        "title": "拖拽文件到这里或点击选择",
        "description": "支持的文件格式和说明",
        "button": "选择文件"
      },
      "processing": "处理中...",
      "result": {
        "title": "处理结果",
        "files": "个文件",
        "downloadAll": "下载全部",
        "download": "下载",
        "clear": "清空"
      },
      "info": {
        "title": "使用说明",
        "items": [
          "说明项目1",
          "说明项目2",
          "说明项目3"
        ]
      }
    }
  }
}
```

#### 英文 (`messages/en.json`)
```json
{
  "tools": {
    "toolName": {
      "name": "Tool Name",
      "description": "Tool description",
      "title": "Tool Title",
      "subtitle": "Tool subtitle",
      "settings": {
        "title": "Settings",
        "description": "Configure processing parameters",
        "option1": "Option 1",
        "option2": "Option 2",
        "placeholder1": "Enter option 1...",
        "placeholder2": "Enter option 2..."
      },
      "upload": {
        "title": "Drag files here or click to select",
        "description": "Supported file formats and instructions",
        "button": "Select Files"
      },
      "processing": "Processing...",
      "result": {
        "title": "Results",
        "files": "files",
        "downloadAll": "Download All",
        "download": "Download",
        "clear": "Clear"
      },
      "info": {
        "title": "Instructions",
        "items": [
          "Instruction item 1",
          "Instruction item 2",
          "Instruction item 3"
        ]
      }
    }
  }
}
```

### 4. 使用步骤

1. **复制模板文件**到新工具目录
2. **替换所有 `toolName` 和 `tool-name`** 为实际名称
3. **实现 `processFile` 函数**的具体逻辑
4. **更新翻译文件**中的内容
5. **添加到导航菜单**中
6. **测试功能**并优化

### 5. 常用依赖

根据工具类型添加相应依赖：

```bash
# 图片处理
pnpm add browser-image-compression fabric cropperjs

# PDF处理  
pnpm add pdf-lib react-pdf

# 文件操作
pnpm add file-saver jszip papaparse

# 加密和编码
pnpm add crypto-js uuid qrcode

# 日期时间
pnpm add date-fns
```

这个模板提供了完整的工具开发框架，包含了所有必要的功能和最佳实践。
