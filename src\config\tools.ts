export interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  path: string;
  icon: string;
  keywords: string[];
  implemented?: boolean; // 默认为 true，设置为 false 则不显示
}

export const toolCategories = {
  image: {
    name: "图片工具",
    icon: "Image",
    description: "图片处理相关工具"
  },
  text: {
    name: "文本工具", 
    icon: "FileText",
    description: "文本处理相关工具"
  },
  data: {
    name: "数据工具",
    icon: "Database", 
    description: "数据结构处理工具"
  },
  utility: {
    name: "实用工具",
    icon: "Wrench",
    description: "常用实用工具"
  },
  pdf: {
    name: "PDF工具",
    icon: "FileType",
    description: "PDF文档处理工具"
  }
};

export const tools: Tool[] = [
  // 图片工具
  {
    id: "image-compressor",
    name: "图片压缩器",
    description: "支持多种格式，可调节压缩质量",
    category: "image",
    path: "/tools/image-compressor",
    icon: "Minimize2",
    keywords: ["图片", "压缩", "优化", "减小", "文件大小"]
  },
  {
    id: "image-converter",
    name: "图片格式转换",
    description: "JPG/PNG/WebP/AVIF 互转",
    category: "image",
    path: "/tools/image-converter",
    icon: "RefreshCw",
    keywords: ["图片", "转换", "格式", "JPG", "PNG", "WebP"],
    implemented: true
  },
  {
    id: "image-cropper",
    name: "图片裁切工具",
    description: "自由裁切、固定比例裁切",
    category: "image",
    path: "/tools/image-cropper",
    icon: "Crop",
    keywords: ["图片", "裁切", "剪切", "尺寸"],
    implemented: false
  },
  {
    id: "image-resizer",
    name: "图片尺寸调整",
    description: "批量调整图片大小",
    category: "image",
    path: "/tools/image-resizer",
    icon: "Maximize2",
    keywords: ["图片", "尺寸", "大小", "调整", "缩放"],
    implemented: false
  },
  {
    id: "image-watermark",
    name: "图片水印添加",
    description: "文字或图片水印",
    category: "image",
    path: "/tools/image-watermark",
    icon: "Type",
    keywords: ["图片", "水印", "文字", "标记"],
    implemented: false
  },
  {
    id: "image-joiner",
    name: "图片拼接器",
    description: "多张图片横向/纵向拼接",
    category: "image",
    path: "/tools/image-joiner",
    icon: "Grid",
    keywords: ["图片", "拼接", "合并", "组合"],
    implemented: false
  },

  // 文本工具
  {
    id: "text-diff",
    name: "文本差异对比",
    description: "高亮显示两段文本的差异",
    category: "text",
    path: "/tools/text-diff",
    icon: "GitCompare",
    keywords: ["文本", "对比", "差异", "比较"],
    implemented: false
  },
  {
    id: "markdown-converter",
    name: "Markdown 转换器",
    description: "Markdown 与 HTML 互转",
    category: "text",
    path: "/tools/markdown-converter",
    icon: "FileCode",
    keywords: ["Markdown", "HTML", "转换", "格式"],
    implemented: false
  },
  {
    id: "text-analyzer",
    name: "文本统计分析",
    description: "字数、词频、可读性分析",
    category: "text",
    path: "/tools/text-analyzer",
    icon: "BarChart3",
    keywords: ["文本", "统计", "分析", "字数", "词频"],
    implemented: false
  },
  {
    id: "text-formatter",
    name: "文本格式化",
    description: "JSON/XML/CSS 美化",
    category: "text",
    path: "/tools/text-formatter",
    icon: "Code",
    keywords: ["格式化", "美化", "JSON", "XML", "CSS"],
    implemented: false
  },
  {
    id: "regex-tester",
    name: "正则表达式测试",
    description: "实时测试正则表达式",
    category: "text",
    path: "/tools/regex-tester",
    icon: "Search",
    keywords: ["正则", "表达式", "测试", "匹配"],
    implemented: false
  },
  {
    id: "text-crypto",
    name: "文本加密解密",
    description: "Base64、MD5、SHA 等",
    category: "text",
    path: "/tools/text-crypto",
    icon: "Lock",
    keywords: ["加密", "解密", "Base64", "MD5", "SHA"],
    implemented: false
  },
  {
    id: "qr-generator",
    name: "二维码生成器",
    description: "文本转二维码",
    category: "text",
    path: "/tools/qr-generator",
    icon: "QrCode",
    keywords: ["二维码", "生成", "QR", "码"]
  },

  // 数据工具
  {
    id: "json-formatter",
    name: "JSON 格式化",
    description: "美化、压缩、验证 JSON",
    category: "data",
    path: "/tools/json-formatter",
    icon: "Braces",
    keywords: ["JSON", "格式化", "美化", "验证"]
  },
  {
    id: "csv-converter",
    name: "CSV 转换器",
    description: "CSV 与 JSON/Excel 互转",
    category: "data",
    path: "/tools/csv-converter",
    icon: "Table",
    keywords: ["CSV", "转换", "JSON", "Excel"],
    implemented: false
  },
  {
    id: "url-encoder",
    name: "URL 编码解码",
    description: "URL 参数编码解码",
    category: "data",
    path: "/tools/url-encoder",
    icon: "Link",
    keywords: ["URL", "编码", "解码", "参数"]
  },
  {
    id: "timestamp-converter",
    name: "时间戳转换",
    description: "Unix 时间戳与日期互转",
    category: "data",
    path: "/tools/timestamp-converter",
    icon: "Clock",
    keywords: ["时间戳", "时间", "日期", "转换", "Unix"]
  },
  {
    id: "color-converter",
    name: "颜色转换器",
    description: "HEX/RGB/HSL 颜色值互转",
    category: "data",
    path: "/tools/color-converter",
    icon: "Palette",
    keywords: ["颜色", "转换", "HEX", "RGB", "HSL"],
    implemented: false
  },
  {
    id: "uuid-generator",
    name: "UUID 生成器",
    description: "生成各种版本的 UUID",
    category: "data",
    path: "/tools/uuid-generator",
    icon: "Hash",
    keywords: ["UUID", "生成", "唯一", "标识符"]
  },

  // 实用工具
  {
    id: "password-generator",
    name: "密码生成器",
    description: "自定义规则生成强密码",
    category: "utility",
    path: "/tools/password-generator",
    icon: "Key",
    keywords: ["密码", "生成", "安全", "强密码"]
  },
  {
    id: "hash-calculator",
    name: "哈希计算器",
    description: "文件或文本哈希值计算",
    category: "utility",
    path: "/tools/hash-calculator",
    icon: "Calculator",
    keywords: ["哈希", "计算", "MD5", "SHA", "校验"]
  },
  {
    id: "unit-converter",
    name: "单位转换器",
    description: "长度、重量、温度等单位转换",
    category: "utility",
    path: "/tools/unit-converter",
    icon: "ArrowLeftRight",
    keywords: ["单位", "转换", "长度", "重量", "温度"]
  },
  {
    id: "base-converter",
    name: "进制转换器",
    description: "二进制、八进制、十六进制转换",
    category: "utility",
    path: "/tools/base-converter",
    icon: "Binary",
    keywords: ["进制", "转换", "二进制", "八进制", "十六进制"],
    implemented: false
  },
  {
    id: "random-generator",
    name: "随机数生成",
    description: "指定范围内的随机数生成",
    category: "utility",
    path: "/tools/random-generator",
    icon: "Shuffle",
    keywords: ["随机数", "生成", "范围", "数字"],
    implemented: false
  },

  // PDF工具
  {
    id: "pdf-merger",
    name: "PDF 合并器",
    description: "将多个 PDF 文件合并为一个",
    category: "pdf",
    path: "/tools/pdf-merger",
    icon: "FileStack",
    keywords: ["PDF", "合并", "文件", "组合"]
  },
  {
    id: "pdf-splitter",
    name: "PDF 分割器",
    description: "将 PDF 按页数分割成多个文件",
    category: "pdf",
    path: "/tools/pdf-splitter",
    icon: "Scissors",
    keywords: ["PDF", "分割", "页面", "拆分"]
  },
  {
    id: "pdf-to-image",
    name: "PDF 转图片",
    description: "将 PDF 页面转换为 JPG/PNG 图片",
    category: "pdf",
    path: "/tools/pdf-to-image",
    icon: "FileImage",
    keywords: ["PDF", "转换", "图片", "JPG", "PNG"]
  },
  {
    id: "fba-label-stamper",
    name: "免费 亚马逊 FBA 标签在线一键添加 Made in China",
    description: "为亚马逊 FBA 标签PDF文件在线一键添加 Made in China 文本",
    category: "pdf",
    path: "/tools/fba-label-stamper",
    icon: "Tag",
    keywords: ["FBA", "在线","亚马逊运营工具", "亚马逊", "Made in China", "PDF"]
  }
];

// 获取所有已实现的工具
export function getImplementedTools(): Tool[] {
  return tools.filter(tool => tool.implemented !== false);
}

export function getToolsByCategory(category: string): Tool[] {
  return tools.filter(tool =>
    tool.category === category && tool.implemented !== false
  );
}

export function getToolById(id: string): Tool | undefined {
  return tools.find(tool => tool.id === id);
}

export function searchTools(query: string): Tool[] {
  const lowercaseQuery = query.toLowerCase();
  return tools.filter(tool =>
    tool.implemented !== false &&
    (tool.name.toLowerCase().includes(lowercaseQuery) ||
    tool.description.toLowerCase().includes(lowercaseQuery) ||
    tool.keywords.some(keyword => keyword.toLowerCase().includes(lowercaseQuery)))
  );
}
