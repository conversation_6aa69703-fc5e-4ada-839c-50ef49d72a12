#!/usr/bin/env node

/**
 * 验证结构化数据脚本
 * 用于检查页面中的JSON-LD结构化数据
 */

const puppeteer = require('puppeteer');

async function verifyStructuredData(url) {
  console.log(`🔍 正在验证页面: ${url}`);
  
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    await page.goto(url, { waitUntil: 'networkidle0' });
    
    // 提取所有JSON-LD结构化数据
    const structuredData = await page.evaluate(() => {
      const scripts = document.querySelectorAll('script[type="application/ld+json"]');
      return Array.from(scripts).map(script => {
        try {
          return JSON.parse(script.textContent);
        } catch (e) {
          return { error: 'JSON解析错误', content: script.textContent };
        }
      });
    });
    
    console.log('\n📊 发现的结构化数据:');
    console.log('='.repeat(50));
    
    structuredData.forEach((data, index) => {
      console.log(`\n${index + 1}. ${data['@type'] || '未知类型'}:`);
      console.log(JSON.stringify(data, null, 2));
    });
    
    // 验证必要的Schema类型
    const schemaTypes = structuredData.map(data => data['@type']).filter(Boolean);
    
    console.log('\n✅ 验证结果:');
    console.log('='.repeat(50));
    console.log(`📋 发现的Schema类型: ${schemaTypes.join(', ')}`);
    
    // 检查必要的Schema
    const requiredSchemas = ['SoftwareApplication', 'BreadcrumbList'];
    const missingSchemas = requiredSchemas.filter(schema => !schemaTypes.includes(schema));
    
    if (missingSchemas.length === 0) {
      console.log('🎉 所有必要的结构化数据都已正确实现！');
    } else {
      console.log(`⚠️  缺少以下结构化数据: ${missingSchemas.join(', ')}`);
    }
    
    // 验证数据完整性
    structuredData.forEach((data, index) => {
      console.log(`\n🔍 验证第${index + 1}个结构化数据:`);
      
      if (data['@type'] === 'SoftwareApplication') {
        const required = ['name', 'description', 'url', 'applicationCategory'];
        const missing = required.filter(field => !data[field]);
        
        if (missing.length === 0) {
          console.log('  ✅ SoftwareApplication 数据完整');
        } else {
          console.log(`  ❌ SoftwareApplication 缺少字段: ${missing.join(', ')}`);
        }
      }
      
      if (data['@type'] === 'BreadcrumbList') {
        if (data.itemListElement && data.itemListElement.length > 0) {
          console.log(`  ✅ BreadcrumbList 包含 ${data.itemListElement.length} 个导航项`);
        } else {
          console.log('  ❌ BreadcrumbList 缺少导航项');
        }
      }
    });
    
  } catch (error) {
    console.error('❌ 验证过程中出错:', error.message);
  } finally {
    await browser.close();
  }
}

// 验证多个页面
async function verifyMultiplePages() {
  const baseUrl = 'http://localhost:3000';
  const pages = [
    '/zh',
    '/zh/tools/qr-generator',
    '/zh/tools/uuid-generator',
    '/zh/tools/timestamp-converter'
  ];
  
  for (const page of pages) {
    await verifyStructuredData(`${baseUrl}${page}`);
    console.log('\n' + '='.repeat(80) + '\n');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const url = process.argv[2];
  
  if (url) {
    verifyStructuredData(url);
  } else {
    console.log('🚀 验证本地开发服务器的结构化数据...');
    verifyMultiplePages();
  }
}

module.exports = { verifyStructuredData };
