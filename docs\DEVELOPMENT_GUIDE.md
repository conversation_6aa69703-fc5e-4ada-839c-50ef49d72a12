# AnyTool 开发指南

## 📋 项目概述

AnyTool 是一个基于 Next.js 15 的多语言在线工具集合网站，提供图片处理、文本转换、数据操作等多种实用工具。

### 技术栈
- **框架**: Next.js 15.3.5 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS v4
- **国际化**: next-intl
- **包管理**: pnpm
- **UI组件**: Radix UI + 自定义组件

## 🏗️ 项目架构

### 目录结构
```
src/
├── app/
│   ├── [locale]/                    # 多语言路由
│   │   ├── layout.tsx              # 语言布局
│   │   ├── page.tsx                # 首页
│   │   └── tools/                  # 工具页面
│   │       └── [tool-name]/
│   │           ├── page.tsx        # 服务端页面（SEO元数据）
│   │           └── ToolNameClient.tsx # 客户端组件（交互逻辑）
│   ├── layout.tsx                  # 根布局
│   ├── globals.css                 # 全局样式
│   └── favicon.ico
├── components/                     # 可复用组件
│   ├── ui/                        # 基础UI组件
│   └── TopNavigation.tsx          # 顶部导航
├── lib/                           # 工具库
│   └── metadata.ts               # SEO元数据生成
├── messages/                      # 国际化文件
│   ├── zh.json                   # 中文翻译
│   └── en.json                   # 英文翻译
└── middleware.ts                  # 路由中间件
```

## 🛠️ 新工具开发流程

### 1. 创建工具页面结构

#### 1.1 服务端页面 (`page.tsx`)
```typescript
import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import { getTranslations } from 'next-intl/server';
import { getToolById } from '@/config/tools';
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import ToolNameClient from './ToolNameClient';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: 'toolName',           // 翻译键名
    path: '/tools/tool-name'        // URL路径
  });
}

export default async function ToolNamePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  const t = await getTranslations('tools.toolName');
  const categoryT = await getTranslations('navigation.categories');

  // 获取工具信息
  const tool = getToolById('tool-name')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);

  // 生成结构化数据
  const softwareSchema = generateSoftwareApplicationSchema(
    tool,
    locale,
    toolName,
    toolDescription
  );

  const breadcrumbSchema = generateBreadcrumbSchema(
    [
      { name: '首页', url: `/${locale}` },
      { name: categoryName, url: `/${locale}#${tool.category}` },
      { name: toolName, url: `/${locale}${tool.path}` }
    ],
    locale
  );

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      <ToolNameClient />
    </>
  );
}
```

#### 1.2 客户端组件 (`ToolNameClient.tsx`)
```typescript
"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { Upload, Download, Settings, Info } from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function ToolNameClient() {
  const t = useTranslations('tools.toolName');
  
  // 状态管理
  const [isProcessing, setIsProcessing] = useState(false);
  
  // 事件处理
  const handleProcess = useCallback(async () => {
    setIsProcessing(true);
    try {
      // 处理逻辑
    } catch (error) {
      console.error('处理失败:', error);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />
      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>

          {/* 工具功能区域 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                {t('upload.title')}
              </CardTitle>
              <CardDescription>{t('upload.description')}</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 工具具体功能 */}
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
```

### 2. 添加国际化支持

#### 2.1 更新翻译文件

在 `messages/zh.json` 中添加：
```json
{
  "tools": {
    "toolName": {
      "name": "工具名称",
      "description": "工具描述",
      "title": "工具标题",
      "subtitle": "工具副标题",
      "upload": {
        "title": "上传文件",
        "description": "选择要处理的文件"
      },
      "info": {
        "title": "使用说明",
        "items": [
          "说明项目1",
          "说明项目2"
        ]
      }
    }
  }
}
```

在 `messages/en.json` 中添加对应的英文翻译。

#### 2.2 更新导航翻译

在翻译文件的 `navigation.tools` 部分添加工具名称：
```json
{
  "navigation": {
    "tools": {
      "tool-name": "Tool Name"
    }
  }
}
```

### 3. 更新导航配置

在 `src/components/TopNavigation.tsx` 中添加工具到相应分类：

```typescript
const toolCategories = {
  image: [
    // ... 现有工具
    { name: 'tool-name', href: '/tools/tool-name' }
  ]
  // ... 其他分类
};
```

## 📝 开发规范

### 1. 文件命名规范
- 页面文件：`page.tsx`（服务端）
- 客户端组件：`ToolNameClient.tsx`（PascalCase）
- 工具路径：`tool-name`（kebab-case）
- 翻译键名：`toolName`（camelCase）

### 2. 组件结构规范
- 所有交互逻辑必须在客户端组件中
- 服务端页面只负责元数据生成
- 使用 `"use client"` 指令标记客户端组件
- 统一使用 `useTranslations` 进行国际化

### 3. SEO优化规范
- 必须使用 `generateToolMetadata` 生成元数据
- 确保 hreflang、canonical URL 正确设置
- 包含完整的 OpenGraph 和 Twitter 元数据
- **必须添加结构化数据**：每个工具页面都需要包含 SoftwareApplication 和 BreadcrumbList Schema
- 结构化数据必须在服务器组件中生成，不能在客户端组件中使用
- **Sitemap 自动生成**：新工具会自动包含在 sitemap.xml 中，无需手动配置

### 4. 样式规范
- 使用 Tailwind CSS 进行样式设计
- 遵循响应式设计原则
- 使用统一的颜色和间距系统
- 保持与现有工具的视觉一致性

### 5. 状态管理规范
- 使用 React Hooks 进行状态管理
- 合理使用 `useCallback` 和 `useMemo` 优化性能
- 错误处理要完善，提供用户友好的错误信息

## 📊 结构化数据集成

### 概述
每个工具页面都必须包含结构化数据以优化 SEO 效果。结构化数据使用 Schema.org 标准，帮助搜索引擎更好地理解和展示我们的工具。

### 必需的 Schema 类型
1. **SoftwareApplication** - 描述工具应用信息
2. **BreadcrumbList** - 描述页面导航路径
3. **WebSite** - 网站信息（在根布局中自动包含）
4. **Organization** - 组织信息（在根布局中自动包含）

### 实现步骤

#### 1. 导入必需模块
```typescript
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import { getToolById } from '@/config/tools';
import { getTranslations } from 'next-intl/server';
```

#### 2. 在服务器组件中生成数据
```typescript
export default async function ToolPage({ params }) {
  const { locale } = await params;
  const t = await getTranslations('tools.toolName');
  const categoryT = await getTranslations('navigation.categories');

  // 获取工具信息
  const tool = getToolById('tool-id')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);

  // 生成结构化数据
  const softwareSchema = generateSoftwareApplicationSchema(
    tool, locale, toolName, toolDescription
  );

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: '首页', url: `/${locale}` },
    { name: categoryName, url: `/${locale}#${tool.category}` },
    { name: toolName, url: `/${locale}${tool.path}` }
  ], locale);

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      <ToolClient />
    </>
  );
}
```

#### 3. 验证结构化数据
```bash
# 验证单个页面
node scripts/check-structured-data.js

# 批量验证所有工具页面
node scripts/verify-all-tools-structured-data.js
```

### 批量管理工具

#### 批量添加结构化数据
```bash
# 为所有工具页面添加结构化数据
node scripts/add-structured-data-to-tools.js

# 恢复备份文件（如果需要）
node scripts/add-structured-data-to-tools.js restore
```

### 验证方法

#### 1. 本地验证
在浏览器控制台运行：
```javascript
const scripts = document.querySelectorAll('script[type="application/ld+json"]');
console.log(`发现 ${scripts.length} 个结构化数据块`);
scripts.forEach((script, i) => {
  const data = JSON.parse(script.textContent);
  console.log(`Schema ${i+1}: ${data['@type']} - ${data.name || '未命名'}`);
});
```

#### 2. 在线验证工具
- **Schema.org 验证器**: https://validator.schema.org/
- **Google Rich Results Test**: https://search.google.com/test/rich-results

### 常见错误避免

❌ **错误做法**:
```typescript
// 不要在客户端组件中使用结构化数据
'use client';
export default function ToolClient() {
  return (
    <>
      <StructuredData data={schema} /> {/* 不会正确渲染 */}
      <div>工具内容</div>
    </>
  );
}
```

✅ **正确做法**:
```typescript
// 在服务器组件中生成结构化数据
export default async function ToolPage() {
  const schema = generateSchema();
  return (
    <>
      <StructuredData data={schema} />
      <ToolClient />
    </>
  );
}
```

### SEO 效果预期
- 🆓 **免费标识**: 搜索结果显示"免费"标签
- 📱 **应用类型**: 显示"Web应用"标识
- 🗂️ **面包屑**: 显示导航路径
- ⭐ **功能列表**: 突出显示核心功能
- 📈 **排名提升**: 预期关键词排名提升 5-15 位
- 📊 **点击率**: 预期点击率提升 20-40%

详细的结构化数据开发指南请参考：`docs/structured-data-development-guide.md`

## 🔧 常用工具库推荐

### 图片处理
- `browser-image-compression` - 图片压缩
- `fabric.js` - 图片编辑和画布操作
- `cropperjs` - 图片裁剪
- `html2canvas` - 截图功能

### PDF处理
- `pdf-lib` - PDF操作（合并、分割、编辑）
- `react-pdf` - PDF预览和渲染

### 文件处理
- `file-saver` - 文件下载
- `papaparse` - CSV解析
- `jszip` - ZIP文件操作

### 数据处理
- `crypto-js` - 加密解密
- `uuid` - UUID生成
- `qrcode` - 二维码生成

## 🚀 部署和测试

### 开发环境
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 类型检查
pnpm type-check

# 代码格式化
pnpm format
```

### 测试清单
- [ ] 页面正常渲染
- [ ] 多语言切换正常
- [ ] SEO元数据正确生成
- [ ] **结构化数据验证通过**（使用 `node scripts/check-structured-data.js` 验证）
- [ ] 工具功能正常工作
- [ ] 响应式设计适配
- [ ] 错误处理完善
- [ ] 性能优化到位

## 📚 最佳实践

### 1. 性能优化
- 使用动态导入减少初始包大小
- 合理使用 React.memo 和 useMemo
- 图片和文件处理使用 Web Workers（如需要）
- 避免不必要的重渲染

### 2. 用户体验
- 提供清晰的加载状态
- 实现拖拽上传功能
- 支持批量处理
- 提供进度反馈

### 3. 安全性
- 所有处理都在浏览器端进行
- 不上传敏感数据到服务器
- 验证用户输入
- 处理异常情况

### 4. 可维护性
- 保持代码结构一致
- 添加适当的注释
- 使用 TypeScript 类型定义
- 遵循 ESLint 规则

## 🔄 持续改进

1. **定期更新依赖**：保持技术栈最新
2. **性能监控**：使用 Web Vitals 监控性能
3. **用户反馈**：收集用户使用反馈
4. **功能扩展**：根据需求添加新工具
5. **代码重构**：定期优化代码结构

## 🎯 实际开发示例

### 示例：创建图片格式转换工具

#### 1. 创建页面结构
```bash
mkdir -p src/app/[locale]/tools/image-converter
```

#### 2. 服务端页面
```typescript
// src/app/[locale]/tools/image-converter/page.tsx
import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import ImageConverterClient from './ImageConverterClient';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: 'imageConverter',
    path: '/tools/image-converter'
  });
}

export default function ImageConverterPage() {
  return <ImageConverterClient />;
}
```

#### 3. 客户端组件核心逻辑
```typescript
// 文件处理
const handleFileSelect = useCallback(async (files: FileList) => {
  setIsProcessing(true);
  const newImages: ConvertedImage[] = [];

  for (const file of Array.from(files)) {
    try {
      const convertedBlob = await convertImageFormat(file, targetFormat, quality);
      const url = URL.createObjectURL(convertedBlob);

      newImages.push({
        original: file,
        converted: convertedBlob,
        url,
        originalFormat: file.type,
        targetFormat: `image/${targetFormat}`
      });
    } catch (error) {
      console.error('转换失败:', error);
    }
  }

  setImages(prev => [...prev, ...newImages]);
  setIsProcessing(false);
}, [targetFormat, quality]);

// 格式转换函数
const convertImageFormat = async (
  file: File,
  format: string,
  quality: number
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);

      canvas.toBlob(
        (blob) => blob ? resolve(blob) : reject(new Error('转换失败')),
        `image/${format}`,
        quality
      );
    };

    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};
```

## 🔍 调试和故障排除

### 常见问题及解决方案

#### 1. 元数据生成问题
**问题**: hreflang 或 canonical URL 不正确
**解决**: 检查 `generateToolMetadata` 参数是否正确

#### 2. 翻译缺失问题
**问题**: 页面显示翻译键而非实际文本
**解决**: 确保翻译文件中包含所有必要的键值对

#### 3. 客户端组件渲染问题
**问题**: 组件不响应用户交互
**解决**: 确保使用了 `"use client"` 指令

#### 4. 文件处理性能问题
**问题**: 大文件处理导致页面卡顿
**解决**: 使用 Web Workers 或分块处理

### 调试工具
- **React Developer Tools**: 组件状态调试
- **Next.js DevTools**: 路由和性能分析
- **浏览器开发者工具**: 网络请求和性能监控

## 📊 性能优化指南

### 1. 代码分割
```typescript
// 动态导入大型库
const PDFLib = dynamic(() => import('pdf-lib'), {
  loading: () => <div>加载PDF处理库...</div>
});
```

### 2. 图片优化
```typescript
// 使用 Next.js Image 组件
import Image from 'next/image';

<Image
  src="/tool-icon.png"
  alt="工具图标"
  width={24}
  height={24}
  priority={false}
/>
```

### 3. 内存管理
```typescript
// 及时清理 Object URLs
useEffect(() => {
  return () => {
    images.forEach(img => URL.revokeObjectURL(img.url));
  };
}, [images]);
```

## 🔐 安全最佳实践

### 1. 输入验证
```typescript
const validateFile = (file: File): boolean => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    throw new Error('不支持的文件类型');
  }

  // 检查文件大小
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    throw new Error('文件过大');
  }

  return true;
};
```

### 2. 错误处理
```typescript
const handleError = (error: Error, context: string) => {
  console.error(`${context}:`, error);
  setError(`${context}: ${error.message}`);

  // 可选：发送错误报告
  // analytics.track('error', { context, error: error.message });
};
```

## 📱 响应式设计指南

### 1. 断点使用
```css
/* Tailwind CSS 断点 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
2xl: 1536px /* 超超大屏幕 */
```

### 2. 响应式布局示例
```typescript
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* 移动端单列，平板双列，桌面三列 */}
</div>

<div className="flex flex-col md:flex-row gap-4">
  {/* 移动端垂直布局，桌面端水平布局 */}
</div>
```

## 🚀 部署清单

### 上线前检查
- [ ] 所有翻译文件完整
- [ ] SEO元数据正确配置
- [ ] **结构化数据验证通过**
  - [ ] SoftwareApplication Schema 存在且完整
  - [ ] BreadcrumbList Schema 存在且正确
  - [ ] Schema.org 验证器无错误
  - [ ] Google Rich Results Test 通过
- [ ] 响应式设计测试通过
- [ ] 性能指标达标
- [ ] 错误处理完善
- [ ] 浏览器兼容性测试
- [ ] 无障碍访问性检查

### 环境变量配置
```env
# .env.local
NEXT_PUBLIC_BASE_URL=https://anytool.example.com
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### Google Analytics 集成
项目已集成 Google Analytics 4 (GA4) 用于用户行为跟踪：

#### 基础配置
1. 在 `.env.local` 中设置 `NEXT_PUBLIC_GA_ID`
2. GA 会自动跟踪页面浏览和基础用户行为

#### 工具页面中的事件跟踪
```typescript
import { trackToolUsage, trackEvent, trackFileProcessing } from '@/components/GoogleAnalytics';

// 跟踪工具使用
const handleToolStart = () => {
  trackToolUsage('Tool Name', 'tool_started');
};

// 跟踪文件处理
const handleFileUpload = (file: File) => {
  trackFileProcessing('Tool Name', file.type, file.size);
};

// 跟踪自定义事件
const handleCustomAction = () => {
  trackEvent('custom_action', 'Tool Usage', 'Action Description');
};
```

详细的 Google Analytics 集成指南请参考：`docs/google-analytics-integration.md`

## 🗺️ Sitemap 自动生成

### 概述
项目的 `sitemap.xml` 会自动从工具配置文件生成，确保搜索引擎能够发现所有已实现的工具页面。

### 自动化特性
- ✅ **从配置文件读取**：自动读取 `src/config/tools.ts` 中的工具列表
- ✅ **过滤已实现工具**：只包含 `implemented !== false` 的工具
- ✅ **多语言支持**：为每种支持的语言生成对应的 URL
- ✅ **正确的 hreflang**：自动生成多语言 alternates
- ✅ **SEO 优化**：设置合适的优先级和更新频率

### 工作原理
```typescript
// 自动从配置文件获取已实现的工具
const implementedTools = tools.filter(tool => tool.implemented !== false);

// 为每种语言和每个工具生成 URL
locales.forEach(locale => {
  implementedTools.forEach(tool => {
    sitemap.push({
      url: `${baseUrl}/${locale}${tool.path}`,
      alternates: {
        languages: generateAlternates(tool.path)
      }
    });
  });
});
```

### 新工具添加流程
1. **在 `tools.ts` 中添加工具配置**
2. **实现工具页面**
3. **Sitemap 自动更新** - 无需手动配置

### 测试 Sitemap
```bash
# 测试 sitemap 生成逻辑
node scripts/test-sitemap.js

# 访问生成的 sitemap
curl http://localhost:3000/sitemap.xml
```

### 验证要点
- ✅ 所有已实现的工具都包含在 sitemap 中
- ✅ 未实现的工具（`implemented: false`）被正确排除
- ✅ 每个 URL 都有正确的多语言 alternates
- ✅ 优先级设置合理（首页 1.0，工具页面 0.8）

---

遵循此开发指南，可以确保新工具的开发质量和一致性，同时保持良好的用户体验和SEO优化效果。

**记住**:
- 不再需要 `convert-tool-pages.js` 脚本，所有新工具都应该按照 Next.js App Router 的标准规范从一开始就正确构建
- 每个新工具页面都必须包含完整的结构化数据，这是 SEO 优化的重要组成部分

## 📚 相关文档

### 结构化数据相关
- [结构化数据开发指南](./structured-data-development-guide.md) - 详细的实现指南和最佳实践
- [结构化数据验证修复报告](./schema-validation-fixes.md) - 常见问题和解决方案
- [结构化数据影响分析](./structured-data-impact-analysis.md) - SEO 效果分析

### 外部资源
- [Schema.org 官方文档](https://schema.org/)
- [Google 结构化数据指南](https://developers.google.com/search/docs/appearance/structured-data)
- [Next.js 国际化文档](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
