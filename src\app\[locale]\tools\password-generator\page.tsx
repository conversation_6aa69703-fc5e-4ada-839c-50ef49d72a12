import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import { getTranslations } from 'next-intl/server';
import { getToolById } from '@/config/tools';
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import PasswordGeneratorClient from './PasswordGeneratorClient';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: 'passwordGenerator',
    path: '/tools/password-generator'
  });
}

export default async function PasswordGeneratorPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  const t = await getTranslations('tools.passwordGenerator');
  const categoryT = await getTranslations('navigation.categories');
  
  // 获取工具信息
  const tool = getToolById('password-generator')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);
  
  // 生成结构化数据
  const softwareSchema = generateSoftwareApplicationSchema(
    tool, 
    locale, 
    toolName, 
    toolDescription
  );
  
  const breadcrumbSchema = generateBreadcrumbSchema(
    [
      { name: '首页', url: `/${locale}` },
      { name: categoryName, url: `/${locale}#${tool.category}` },
      { name: toolName, url: `/${locale}${tool.path}` }
    ],
    locale
  );

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      <PasswordGeneratorClient />
    </>
  );
}
