#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 获取命令行参数
const args = process.argv.slice(2);
if (args.length < 2) {
  console.log('使用方法: node scripts/create-new-tool.js <tool-name> <ToolDisplayName> [category]');
  console.log('示例: node scripts/create-new-tool.js image-resizer "图片尺寸调整" image');
  process.exit(1);
}

const [toolName, displayName, category = 'utility'] = args;
const toolNameCamel = toolName.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
const toolNamePascal = toolNameCamel.charAt(0).toUpperCase() + toolNameCamel.slice(1);

console.log(`🚀 创建新工具: ${toolName}`);
console.log(`📁 工具名称: ${toolName}`);
console.log(`🏷️  显示名称: ${displayName}`);
console.log(`📂 分类: ${category}`);
console.log(`🔧 组件名称: ${toolNamePascal}Client`);

// 创建工具目录
const toolDir = path.join(process.cwd(), 'src/app/[locale]/tools', toolName);
if (!fs.existsSync(toolDir)) {
  fs.mkdirSync(toolDir, { recursive: true });
  console.log(`✅ 创建目录: ${toolDir}`);
} else {
  console.log(`⚠️  目录已存在: ${toolDir}`);
}

// 生成服务端页面文件
const pageContent = `import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import ${toolNamePascal}Client from './${toolNamePascal}Client';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: '${toolNameCamel}',
    path: '/tools/${toolName}'
  });
}

export default function ${toolNamePascal}Page() {
  return <${toolNamePascal}Client />;
}
`;

const pagePath = path.join(toolDir, 'page.tsx');
fs.writeFileSync(pagePath, pageContent);
console.log(`✅ 创建文件: ${pagePath}`);

// 生成客户端组件文件
const clientContent = `"use client";

import { useState, useCallback, useRef } from "react";
import { useTranslations } from "next-intl";
import { Upload, Download, Settings, Info, Loader2 } from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ProcessedFile {
  original: File;
  processed: Blob;
  url: string;
}

export default function ${toolNamePascal}Client() {
  const t = useTranslations('tools.${toolNameCamel}');
  
  const [files, setFiles] = useState<ProcessedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(async (selectedFiles: FileList) => {
    if (!selectedFiles.length) return;
    
    setIsProcessing(true);
    setError(null);
    
    try {
      const processedFiles: ProcessedFile[] = [];
      
      for (const file of Array.from(selectedFiles)) {
        validateFile(file);
        const processedBlob = await processFile(file);
        const url = URL.createObjectURL(processedBlob);
        
        processedFiles.push({
          original: file,
          processed: processedBlob,
          url
        });
      }
      
      setFiles(prev => [...prev, ...processedFiles]);
    } catch (err) {
      setError(err instanceof Error ? err.message : '处理失败');
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const validateFile = (file: File) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件类型');
    }
    
    if (file.size > maxSize) {
      throw new Error('文件过大，请选择小于10MB的文件');
    }
  };

  const processFile = async (file: File): Promise<Blob> => {
    // TODO: 实现具体的文件处理逻辑
    return new Promise((resolve) => {
      setTimeout(() => resolve(file), 1000); // 模拟处理时间
    });
  };

  const downloadFile = useCallback((processedFile: ProcessedFile) => {
    const link = document.createElement('a');
    link.href = processedFile.url;
    link.download = \`processed_\${processedFile.original.name}\`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  const clearResults = useCallback(() => {
    files.forEach(file => URL.revokeObjectURL(file.url));
    setFiles([]);
    setError(null);
  }, [files]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles);
    }
  }, [handleFileSelect]);

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />
      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>

          <Card className="mb-6">
            <CardContent className="p-6">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">{t('upload.title')}</h3>
                <p className="text-muted-foreground mb-4">{t('upload.description')}</p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {t('processing')}
                    </>
                  ) : (
                    t('upload.button')
                  )}
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {error && (
            <Card className="mb-6 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <p className="text-red-600">{error}</p>
              </CardContent>
            </Card>
          )}

          {files.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    {t('result.title')} ({files.length} {t('result.files')})
                  </span>
                  <Button onClick={clearResults} variant="outline" size="sm">
                    {t('result.clear')}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{file.original.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {(file.original.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                      <Button
                        onClick={() => downloadFile(file)}
                        size="sm"
                        variant="outline"
                      >
                        {t('result.download')}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: \`• \${item}\` }} />
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
`;

const clientPath = path.join(toolDir, `${toolNamePascal}Client.tsx`);
fs.writeFileSync(clientPath, clientContent);
console.log(`✅ 创建文件: ${clientPath}`);

// 生成翻译模板
const translationTemplate = {
  [toolNameCamel]: {
    name: displayName,
    description: `${displayName}工具描述`,
    title: displayName,
    subtitle: `${displayName}的副标题描述`,
    upload: {
      title: "拖拽文件到这里或点击选择",
      description: "支持的文件格式和使用说明",
      button: "选择文件"
    },
    processing: "处理中...",
    result: {
      title: "处理结果",
      files: "个文件",
      download: "下载",
      clear: "清空"
    },
    info: {
      title: "使用说明",
      items: [
        "使用说明项目1",
        "使用说明项目2",
        "所有处理都在浏览器中进行，保护隐私安全"
      ]
    }
  }
};

console.log('\\n📝 请将以下内容添加到翻译文件中:');
console.log('\\n--- messages/zh.json 中的 tools 部分 ---');
console.log(JSON.stringify(translationTemplate, null, 2));

console.log('\\n--- messages/zh.json 中的 navigation.tools 部分 ---');
console.log(`"${toolName}": "${displayName}"`);

console.log('\\n🔧 下一步操作:');
console.log('1. 更新翻译文件 (messages/zh.json 和 messages/en.json)');
console.log('2. 在 TopNavigation.tsx 中添加工具到相应分类');
console.log('3. 实现 processFile 函数的具体逻辑');
console.log('4. 根据需要安装相关依赖包');
console.log('5. 测试工具功能');

console.log('\\n✨ 新工具创建完成！');
