<!DOCTYPE html>
<html>
<head>
    <title>Test Structured Data</title>
</head>
<body>
    <h1>Structured Data Test</h1>
    
    <h2>English Version</h2>
    <iframe src="http://localhost:3000/en" width="100%" height="300"></iframe>
    
    <h2>Chinese Version</h2>
    <iframe src="http://localhost:3000/zh" width="100%" height="300"></iframe>
    
    <h2>Japanese Version</h2>
    <iframe src="http://localhost:3000/ja" width="100%" height="300"></iframe>
    
    <h2>German Version</h2>
    <iframe src="http://localhost:3000/de" width="100%" height="300"></iframe>
    
    <h2>Russian Version</h2>
    <iframe src="http://localhost:3000/ru" width="100%" height="300"></iframe>
    
    <script>
        // 测试结构化数据
        async function testStructuredData() {
            const languages = ['en', 'zh', 'ja', 'de', 'ru'];
            
            for (const lang of languages) {
                try {
                    const response = await fetch(`http://localhost:3000/${lang}`);
                    const html = await response.text();
                    
                    // 查找结构化数据
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const scripts = doc.querySelectorAll('script[type="application/ld+json"]');
                    
                    console.log(`\n=== ${lang.toUpperCase()} ===`);
                    console.log(`Found ${scripts.length} structured data scripts`);
                    
                    scripts.forEach((script, index) => {
                        try {
                            const data = JSON.parse(script.textContent);
                            console.log(`\nScript ${index + 1} (${data['@type']}):`);
                            
                            if (data['@type'] === 'WebSite') {
                                console.log(`- Name: ${data.name}`);
                                console.log(`- AlternateName: ${data.alternateName}`);
                                console.log(`- Description: ${data.description}`);
                                console.log(`- Language: ${data.inLanguage}`);
                            } else if (data['@type'] === 'Organization') {
                                console.log(`- Name: ${data.name}`);
                                console.log(`- Description: ${data.description}`);
                                console.log(`- Contact Type: ${data.contactPoint?.contactType}`);
                                console.log(`- Available Languages: ${data.contactPoint?.availableLanguage?.join(', ')}`);
                            }
                        } catch (e) {
                            console.error(`Error parsing script ${index + 1}:`, e.message);
                        }
                    });
                    
                } catch (error) {
                    console.error(`Error testing ${lang}:`, error.message);
                }
            }
        }
        
        // 页面加载后运行测试
        window.addEventListener('load', () => {
            setTimeout(testStructuredData, 2000);
        });
    </script>
</body>
</html>
