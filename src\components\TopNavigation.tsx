"use client";

import Link from "next/link";
import NextImage from "next/image";
import { usePathname, useParams } from "next/navigation";
import { useState, useRef, useCallback, useEffect } from "react";
import { useTranslations } from 'next-intl';
import {
  Menu,
  X,
  Home,
  Image,
  FileText,
  Database,
  Wrench,
  FileType,
  Search,
  Globe,
  ChevronDown
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/ui/menubar";
import { toolCategories, searchTools, getToolsByCategory } from "@/config/tools";
import { cn } from "@/lib/utils";
import { locales, localeLabels, type Locale } from "@/i18n/config";

const categoryIcons = {
  image: Image,
  text: FileText,
  data: Database,
  utility: Wrench,
  pdf: FileType,
};

export function TopNavigation() {
  const pathname = usePathname();
  const params = useParams();
  const locale = params.locale as Locale;
  const t = useTranslations('navigation');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showSearch, setShowSearch] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);

  // 用于延迟关闭语言菜单的定时器（保留语言菜单的鼠标悬停逻辑）
  const languageTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理语言菜单鼠标进入
  const handleLanguageMouseEnter = useCallback(() => {
    if (languageTimeoutRef.current) {
      clearTimeout(languageTimeoutRef.current);
      languageTimeoutRef.current = null;
    }
    setShowLanguageMenu(true);
  }, []);

  // 处理语言菜单鼠标离开
  const handleLanguageMouseLeave = useCallback(() => {
    languageTimeoutRef.current = setTimeout(() => {
      setShowLanguageMenu(false);
    }, 150);
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (languageTimeoutRef.current) {
        clearTimeout(languageTimeoutRef.current);
      }
    };
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const results = searchTools(query);
      setSearchResults(results);
      setShowSearch(true);
    } else {
      setShowSearch(false);
    }
  };

  const currentLocaleLabel = localeLabels[locale];

  return (
    <>
      {/* Top Navigation Bar */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Left side - Logo and Desktop Navigation */}
            <div className="flex items-center">
              {/* Logo */}
              <Link href={`/${locale}`} className="flex items-center space-x-2 mr-8">
                <div className="w-8 h-8 flex items-center justify-center">
                  <NextImage
                    src="/logo.png"
                    alt="AnyTool Logo"
                    width={32}
                    height={32}
                    className="rounded-lg"
                  />
                </div>
                <span className="text-xl font-bold">{t('title')}</span>
              </Link>

              {/* Desktop Navigation - 使用 Radix UI Menubar 重构 */}
              <div className="hidden md:flex items-center space-x-4">
                {/* Home Link - 保持独立，不在 Menubar 内 */}
                <Link
                  href={`/${locale}`}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    pathname === `/${locale}`
                      ? "bg-primary text-primary-foreground"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  <Home className="h-4 w-4 mr-2" />
                  Home
                </Link>

                {/* Category Dropdowns - 使用 Radix UI Menubar */}
                <Menubar className="border-0 bg-transparent p-0 space-x-1">
                  {Object.entries(toolCategories).map(([categoryKey]) => {
                    const Icon = categoryIcons[categoryKey as keyof typeof categoryIcons];
                    const categoryTools = getToolsByCategory(categoryKey);

                    // 检查当前分类是否有活跃的工具页面
                    const isActive = categoryTools.some(tool => pathname === `/${locale}${tool.path}`);

                    return (
                      <MenubarMenu key={categoryKey}>
                        <MenubarTrigger
                          className={cn(
                            "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors cursor-pointer border-0 bg-transparent",
                            isActive
                              ? "bg-blue-600 text-white"
                              : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 data-[state=open]:bg-gray-100 data-[state=open]:text-gray-900"
                          )}
                        >
                          <Icon className="h-4 w-4 mr-2" />
                          {t(`categories.${categoryKey}`)}
                        </MenubarTrigger>

                        {/* MenubarContent 替代原来的手动下拉菜单 */}
                        <MenubarContent className="min-w-[240px] mt-1">
                          {categoryTools.map((tool) => (
                            <MenubarItem key={tool.id} asChild>
                              <Link
                                href={`/${locale}${tool.path}`}
                                className={cn(
                                  "w-full block text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors",
                                  pathname === `/${locale}${tool.path}`
                                    ? "bg-blue-50 text-blue-700 font-semibold border-l-2 border-blue-500"
                                    : ""
                                )}
                              >
                                {t(`tools.${tool.id}`)}
                              </Link>
                            </MenubarItem>
                          ))}
                        </MenubarContent>
                      </MenubarMenu>
                    );
                  })}
                </Menubar>
              </div>
            </div>

            {/* Right side - Search and Language */}
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative hidden md:block">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder={t('search')}
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                
                {/* Search Results */}
                {showSearch && searchResults.length > 0 && (
                  <div className="absolute top-full left-0 mt-1 w-full bg-white rounded-md shadow-lg border border-gray-200 py-2 z-50">
                    {searchResults.slice(0, 5).map((tool) => (
                      <Link
                        key={tool.id}
                        href={`/${locale}${tool.path}`}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowSearch(false)}
                      >
                        {t(`tools.${tool.id}`)}
                      </Link>
                    ))}
                  </div>
                )}
              </div>

              {/* Language Switcher */}
              <div
                className="relative"
                onMouseEnter={handleLanguageMouseEnter}
                onMouseLeave={handleLanguageMouseLeave}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                  className="flex items-center space-x-2"
                >
                  <Globe className="h-4 w-4" />
                  <span className="hidden sm:inline">{currentLocaleLabel}</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>

                {showLanguageMenu && (
                  <div
                    className="absolute top-full right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-2 z-50"
                    onMouseEnter={handleLanguageMouseEnter}
                    onMouseLeave={handleLanguageMouseLeave}
                  >
                    {locales.map((loc) => (
                      <Link
                        key={loc}
                        href={pathname.replace(`/${locale}`, `/${loc}`)}
                        className={cn(
                          "block px-4 py-2 text-sm transition-colors",
                          loc === locale
                            ? "bg-primary text-primary-foreground"
                            : "text-gray-700 hover:bg-gray-100"
                        )}
                        onClick={() => setShowLanguageMenu(false)}
                      >
                        {localeLabels[loc]}
                      </Link>
                    ))}
                  </div>
                )}
              </div>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <div className="px-4 py-4 space-y-4">
              {/* Mobile Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder={t('search')}
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Mobile Navigation Links */}
              <div className="space-y-2">
                <Link
                  href={`/${locale}`}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md text-sm font-medium",
                    pathname === `/${locale}`
                      ? "bg-primary text-primary-foreground"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Home className="h-4 w-4 mr-2" />
                  Home
                </Link>

                {Object.entries(toolCategories).map(([categoryKey]) => {
                  const Icon = categoryIcons[categoryKey as keyof typeof categoryIcons];
                  const categoryTools = getToolsByCategory(categoryKey);
                  
                  return (
                    <div key={categoryKey} className="space-y-1">
                      <div className="flex items-center px-3 py-2 text-sm font-medium text-gray-600">
                        <Icon className="h-4 w-4 mr-2" />
                        {t(`categories.${categoryKey}`)}
                      </div>
                      <div className="ml-6 space-y-1">
                        {categoryTools.map((tool) => (
                          <Link
                            key={tool.id}
                            href={`/${locale}${tool.path}`}
                            className={cn(
                              "block px-3 py-2 rounded-md text-sm",
                              pathname === `/${locale}${tool.path}`
                                ? "bg-primary text-primary-foreground"
                                : "text-gray-700 hover:bg-gray-100"
                            )}
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            {t(`tools.${tool.id}`)}
                          </Link>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
}
