{"metadata": {"title": "AnyTool - Бесплатные онлайн-инструменты", "description": "Предоставляет различные практичные онлайн-инструменты: обработка изображений, обработка текста, конвертация данных, операции с PDF, полностью бесплатно", "keywords": "онлайн-инструменты,сжатие изображений,обработка текста,конвертация данных,PDF инструменты,бесплатные инструменты"}, "structuredData": {"website": {"alternateName": "AnyTool - Коллекция бесплатных онлайн-инструментов", "description": "Бесплатные онлайн-инструменты для обработки изображений, обработки текста, конвертации данных, операций с PDF и многое другое"}, "organization": {"description": "Профессиональная платформа, предоставляющая бесплатные онлайн-инструменты", "contactType": "служба поддержки клиентов"}, "software": {"requirements": "Браузер с поддержкой JavaScript", "features": {"freeToUse": "Бесплатно в использовании", "noRegistration": "Регистрация не требуется", "browserBased": "Обработка в браузере", "privacySecure": "Безопасность конфиденциальности"}}, "itemList": {"toolsCollection": "Коллекция инструментов", "toolsDescription": "Коллекция онлайн-инструментов, связанных с"}}, "navigation": {"title": "AnyTool", "home": "Главная", "search": "Поиск инструментов...", "categories": {"image": "Изображения", "text": "Текст", "data": "Данные", "utility": "Утилиты", "pdf": "PDF"}, "languageSwitch": "Сменить язык", "tools": {"image-compressor": "Сжатие изображений", "image-converter": "Конвертер изображений", "image-cropper": "Обрезка изображений", "image-resizer": "Изменение размера", "image-watermark": "Водяные знаки", "image-joiner": "Объединение изображений", "text-diff": "Сравнение текста", "markdown-converter": "<PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text-analyzer": "Анализатор текста", "text-formatter": "Форматирование текста", "regex-tester": "Тестер регулярных выражений", "text-crypto": "Шифрование текста", "qr-generator": "Генератор QR-кодов", "json-formatter": "Форматирование JSON", "csv-converter": "Конвертер CSV", "url-encoder": "Кодирование URL", "timestamp-converter": "Конвертер временных меток", "color-converter": "Конвертер цветов", "uuid-generator": "Генер<PERSON><PERSON><PERSON><PERSON> UUID", "password-generator": "Генератор паролей", "hash-calculator": "Калькулятор хэшей", "unit-converter": "Конвертер единиц", "base-converter": "Конвертер систем счисления", "random-generator": "Генератор случайных чисел", "pdf-merger": "Объединение PDF", "pdf-splitter": "Разделение PDF", "pdf-to-image": "PDF в изображение", "fba-label-stamper": "Инструмент штампов FBA"}}, "components": {"dragDropUpload": {"clickOrDrag": "Нажмите, чтобы выбрать или перетащите файлы PDF", "supportedFormats": "Поддерживаемые форматы: PDF (макс. {maxSize}МБ)", "selectFile": "Выбрать файл", "size": "Размер", "uploaded": "Загружено", "uploadFailed": "Загрузка не удалась"}, "fbaSettingsPanel": {"textSettings": "Настройки текста", "advancedOptions": "& Расширенные параметры", "addText": "Добавить текст", "textDescription": "Этот текст будет добавлен рядом с обнаруженными позициями \"新品\" или \"New\"", "advancedOptionsLabel": "Расширенные параметры", "fontSize": "Размер шрифта", "fontSizeRange": "px (6-20)", "positionAdjustment": "Регулировка положения", "horizontalOffset": "Горизонтальное смещение", "verticalOffset": "Вертикальное смещение", "positionDescription": "Точная настройка смещения текста относительно обнаруженного положения", "debugMode": "Включить режим отладки", "debugModeDescription": "Показывать области обнаружения текста для отладки проблем с позиционированием"}, "fbaActionButtons": {"processing": "Обработка...", "processFile": "Обработать файл", "downloaded": "Скачано", "downloadResult": "Скачать результат", "reset": "Сброс", "processHint": "💡 Нажмите \"Обработать файл\", чтобы начать добавление меток \"Made in China\" в ваш PDF", "processCompleted": "✅ Обработка завершена!"}}, "home": {"hero": {"title": "Все инструменты в одном месте", "subtitle": "Бесплатные онлайн-инструменты для обработки изображений, текста, конвертации данных и многого другого для ваших повседневных рабочих потребностей", "description": "Повысьте свою продуктивность с помощью профессиональных онлайн-инструментов. Регистрация не требуется, полностью бесплатно.", "cta": "Исследовать инструменты"}, "features": {"title": "Почему выбирают AnyTool", "fast": {"title": "Быстрая обработка", "description": "Вся обработка происходит в вашем браузере, быстро и безопасно"}, "free": {"title": "Полностью бесплатно", "description": "Все инструменты бесплатны и доступны без ограничений"}, "privacy": {"title": "Конфиденциальность", "description": "Файлы не загружаются на сервер, ваша конфиденциальность защищена"}, "easy": {"title": "Простота использования", "description": "Интуитивно понятный интерфейс, технические знания не требуются"}}, "categories": {"title": "Категории инструментов", "viewAll": "Показать все"}, "stats": {"tools": "Инстру<PERSON><PERSON><PERSON><PERSON>ов", "users": "Пользователей", "processes": "Обработок"}}, "tools": {"imageCompressor": {"name": "Сжатие изображений", "description": "Сжимайте изображения JPG, PNG для уменьшения размера файла", "keywords": "сжатие изображений, фотокомпрессор, уменьшение размера файла, сжатие JPG, сжатие PNG, конвертер WebP, оптимизатор изображений, изменение размера фото, качество изображения, уменьшение размера файла, пакетное сжатие, онлайн-инструмент для изображений, оптимизация фото, веб-оптимизация", "title": "Сжатие изображений", "subtitle": "Сжимайте размер файла изображения, сохраняя хорошее визуальное качество", "upload": {"title": "Перетащите изображения сюда или нажмите, чтобы выбрать", "subtitle": "Поддерживаются форматы JPG, PNG, WebP, разрешено несколько файлов", "button": "Выбрать изображения"}, "settings": {"title": "Настройки сжатия", "description": "Отрегулируйте эти параметры для управления эффектом сжатия", "quality": "Качество изображения", "maxWidth": "Макс. ширина (px)", "maxHeight": "Макс. высота (px)", "format": "Выходной формат", "compress": "Начать сжатие"}, "processing": {"title": "Сжатие изображений..."}, "result": {"title": "Результат сжатия", "images": "изображений", "downloadAll": "Скачать все", "clear": "Очистить", "original": "Исходный файл", "compressed": "Сжатый", "reduction": "Уменьшено на", "download": "Скачать"}, "info": {"title": "Инструкции", "items": ["Поддерживает сжатие изображений форматов JPG, PNG, WebP", "Отрегулируйте качество сжатия, чтобы сбалансировать размер файла и качество изображения", "Поддерживает установку максимальной ширины и высоты с автоматическим пропорциональным масштабированием", "Вся обработка выполняется в браузере, без загрузки на сервер", "Загружайте напрямую после сжатия, защищая вашу конфиденциальность"]}}, "imageConverter": {"name": "Конвертер изображений", "description": "Конвертируйте между форматами JPG/PNG/WebP/AVIF", "keywords": "конвертация формата изображения, конвертер изображений, JPG в PNG, PNG в JPG, конвертер WebP, конвертер AVIF, изменение формата изображения, конвертер типов изображений, пакетная конвертация, онлайн-конвертер, инструмент для форматов изображений, обработка изображений", "title": "Конвертер форматов изображений", "subtitle": "Конвертируйте между форматами JPG, PNG, WebP, AVIF, сохраняя качество изображения", "settings": {"title": "Настройки конвертации", "description": "Выберите целевой формат и настройки качества", "format": "Целевой формат", "quality": "Качество изображения"}, "upload": {"title": "Выбрать файлы изображений", "description": "Поддерживает несколько форматов, выбирайте несколько файлов одновременно", "dragText": "Перетащите изображения сюда или нажмите, чтобы выбрать", "selectFiles": "Выбрать файлы изображений", "supportedFormats": "Поддерживаются форматы JPG, PNG, WebP, BMP, GIF", "processing": "Конвертация..."}, "results": {"title": "Результаты конвертации", "downloadAll": "Скачать все", "clearAll": "Очистить все", "download": "Скачать"}, "info": {"title": "Инструкции", "items": ["Поддерживает конвертацию форматов JPG, PNG, WebP, BMP, GIF", "Регулируемое качество JPEG для баланса между размером файла и качеством изображения", "Поддерживает пакетную конвертацию для повышения эффективности", "Вся обработка выполняется в вашем браузере, без загрузки на сервер", "Загружайте конвертированные изображения напрямую, защищая вашу конфиденциальность"]}}, "jsonFormatter": {"name": "Форматировщик JSON", "description": "Форматировать, минифицировать и проверять JSON-данные", "keywords": "форматир<PERSON><PERSON>и<PERSON> JSON, краси<PERSON><PERSON><PERSON><PERSON> JSON, минифик<PERSON><PERSON><PERSON><PERSON>, валидатор JSO<PERSON>, парсер JSON, красивая печать JSON, проверка синтаксиса JSON, редактор JSON, форматировщик ответа API, JSON lint, форматирование данных, просмотрщик JSON, конвертер JSON", "title": "Форматировщик JSON", "subtitle": "Украш<PERSON><PERSON>те, минифицир<PERSON>йте, проверяйте JSON-данные с подсветкой синтаксиса и обнаружением ошибок", "actions": {"title": "Панель действий", "description": "Выберите тип операции и настройки", "format": "Форматировать", "minify": "Мини<PERSON>и<PERSON><PERSON><PERSON><PERSON>ать", "validate": "Проверить", "loadSample": "Загрузить образец", "clear": "Очистить", "indentSize": "Размер отступа", "spaces": "пробелы", "upload": "Загрузить файл", "copy": "Копировать", "copied": "Скопировано", "download": "Скачать"}, "input": {"title": "Входные JSON-данные", "description": "Вставьте или введите JSON-данные для обработки", "placeholder": "Введите JSON-данные здесь..."}, "result": {"title": "Выходной результат", "description": "Отформатированные JSON-данные", "placeholder": "Отформатированный результат будет показан здесь...", "error": "Ошибка JSON", "valid": "Формат JSON действителен", "invalid": "Формат JSON недействителен"}, "info": {"title": "Инструкции", "items": ["<strong>Форматировать:</strong> Преобразуйте минифицированный JSON в читаемый формат", "<strong>Минифицировать:</strong> Удалите все ненужные пробелы и разрывы строк", "<strong>Проверить:</strong> Проверьте правильность синтаксиса JSON", "Поддерживает загрузку файлов .json и .txt", "Настройте размер отступа для управления стилем форматирования", "Вся обработка выполняется в браузере, защищая конфиденциальность данных"]}}, "qrGenerator": {"name": "Генератор QR-кодов", "description": "Генерируйте различные типы QR-кодов", "keywords": "генератор QR-кодов, создатель QR-кодов, генератор штрих-кодов, QR-код WiFi, QR-код URL, контактный QR-код, QR-код SMS, QR-код электронной почты, сканер QR, мобильный QR-код, код быстрого ответа, 2D-штрих-код, создатель QR-кодов", "title": "Генератор QR-кодов", "subtitle": "Генерируйте QR-коды для URL-адресов, текста, WiFi и других типов", "content": {"title": "Ввести содержимое", "description": "Введите текст или ссылку для генерации QR-кода", "placeholder": "Введите текст, ссылки, информацию о WiFi и т.д...", "quickTemplates": "Быстрые шаблоны", "type": "Тип содержимого", "types": {"text": "Обычный текст", "url": "Ссылка на сайт", "email": "Электронная почта", "phone": "Номер телефона", "sms": "SMS", "wifi": "Подключение к WiFi"}, "placeholders": {"text": "Введите текст для генерации QR-кода...", "url": "https://example.com", "email": "<EMAIL>", "phone": "****** 123 4567", "sms": "Введите содержимое SMS...", "wifi": {"ssid": "Имя сети WiFi", "password": "Пароль WiFi", "security": "Тип безопасности"}}}, "settings": {"title": "Пользовательские настройки", "description": "Настройте внешний вид и свойства QR-кода", "size": "Размер", "errorCorrection": "Уровень коррекции ошибок", "errorLevels": {"low": "Низкий (~7%)", "lowDesc": "Подходит для чистых сред", "medium": "Средний (~15%)", "mediumDesc": "Рекомендуется", "quartile": "Квартиль (~25%)", "quartileDesc": "Подходит для общих сред", "high": "Высокий (~30%)", "highDesc": "Подходит для суровых сред"}, "foregroundColor": "Цвет переднего плана", "backgroundColor": "Цвет фона", "generate": "Сгенерировать QR-код"}, "result": {"title": "Предварительный просмотр QR-кода", "description": "Предварительный просмотр сгенерированного QR-кода", "downloadPng": "PNG", "downloadSvg": "SVG", "pixels": "пикселей", "placeholder": "Введите содержимое для генерации QR-кода"}, "info": {"title": "Инструкции", "items": ["Поддерживает текст, ссылки, WiFi, электронную почту, телефон и другие форматы", "Настройте размер, цвет и уровень коррекции ошибок QR-кода", "Поддерживает загрузку в форматах PNG и SVG", "Более высокий уровень коррекции ошибок делает QR-коды более сложными, но более отказоустойчивыми", "Формат WiFi: WIFI:T:WPA;S:NetworkName;P:Password;;", "Вся обработка выполняется в браузере, защищая конфиденциальность и безопасность"]}}, "pdfMerger": {"name": "Объединение PDF", "description": "Объединяйте несколько PDF-файлов в один", "keywords": "объединение PDF, объединить PDF, объединить PDF-файлы, соединитель PDF, комбинатор PDF, соединить PDF, конкатенировать PDF, инструменты PDF, объединение документов, объединение файлов, редактор PDF, пакетная обработка PDF", "title": "Объединение PDF", "subtitle": "Объединяйте несколько PDF-файлов в один файл с сортировкой перетаскиванием и предварительным просмотром", "upload": {"title": "Перетащите PDF-файлы сюда или нажмите, чтобы выбрать", "description": "Поддерживает несколько PDF-файлов, будут объединены в порядке списка", "button": "Выбрать PDF-файлы"}, "fileList": {"title": "Список PDF-файлов ({count} файлов)", "description": "Всего {totalPages} страниц • Перетащите для настройки порядка объединения", "mergeButton": "Объединить PDF", "clearButton": "Очистить", "merging": "Объединение...", "pages": "{count} страниц", "position": "<PERSON>айл {position}"}, "processing": {"message": "Объединение PDF-файлов..."}, "result": {"title": "Объединение завершено", "description": "PDF-файлы успешно объединены, нажмите, чтобы скачать", "downloadButton": "Скачать объединенный PDF"}, "info": {"title": "Инструкции", "items": ["Поддерживает выбор нескольких PDF-файлов для объединения", "Используйте стрелки вверх/вниз для настройки порядка объединения файлов", "Объединенный файл будет содержать все страницы из исходных файлов", "Вся обработка выполняется в браузере, без загрузки на сервер", "Поддерживает загрузку перетаскиванием для упрощения работы"]}}, "pdfSplitter": {"name": "Разделитель PDF", "description": "Разделите PDF на несколько файлов по количеству страниц", "keywords": "разделитель PDF, разделить PDF, разделить PDF, сепаратор PDF, извлечь страницы PDF, экстрактор страниц PDF, разбить PDF, инструменты PDF, разделитель документов, экстрактор диапазона страниц, редактор PDF", "title": "Разделитель PDF", "subtitle": "Разделите PDF-файлы по количеству страниц или пользовательским диапазонам на несколько файлов", "upload": {"title": "Перетащите PDF-файл сюда или нажмите, чтобы выбрать", "subtitle": "Выберите PDF-файл для разделения", "button": "Выбрать PDF-файл"}, "fileInfo": {"title": "Информация о файле", "filename": "Имя файла", "filesize": "Размер файла", "totalPages": "Всего страниц"}, "settings": {"title": "Настройки разделения", "description": "Выберите метод и параметры разделения", "mode": "Метод разделения", "modes": {"pages": "Разделить по количеству страниц", "range": "Пользовательский диапазон"}, "pagesPerFile": "Страниц на файл", "willGenerate": "Будет сгенерировано {count} файлов", "pageRange": "Диа<PERSON><PERSON><PERSON><PERSON><PERSON> страниц (например, 1-3, 5, 7-10)", "pageRangePlaceholder": "1-3, 5, 7-10", "pageRangeDescription": "Разделяйте несколько диапазонов запятыми, поддерживает отдельные страницы и диапазоны страниц", "split": "Начать разделение", "splitting": "Разделение..."}, "processing": {"title": "Разделение PDF-файла..."}, "result": {"title": "Результат разделения", "description": "Нажмите, чтобы скачать отдельные файлы или пакетную загрузку", "downloadAll": "Скачать все", "clear": "Очистить", "download": "Скачать"}, "info": {"title": "Инструкции", "items": ["Разделить по количеству страниц: Разделите PDF равномерно по указанному количеству страниц", "Пользовательский диапазон: Укажите точные диапазоны страниц для разделения", "Формат диапазона: 1-3 означает страницы с 1 по 3, 5 означает страницу 5, разделяйте запятыми", "Вся обработка выполняется в браузере, без загрузки на сервер", "Поддержива<PERSON>т пакетную загрузку всех разделенных файлов"]}}, "pdfToImage": {"name": "PDF в изображение", "description": "Преобразуйте страницы PDF в изображения JPG/PNG", "keywords": "PDF в изображение, PDF в JPG, PDF в PNG, конвертировать PDF, конвертер PDF, извлечь изображения из PDF, страница PDF в изображение, конвертер документов, инструменты PDF, конвертер изображений, экстрактор PDF", "title": "PDF в изображение", "subtitle": "Преобразуйте страницы PDF в изображения JPG или PNG с настраиваемым качеством и размером", "upload": {"title": "Перетащите PDF-файл сюда или нажмите, чтобы выбрать", "subtitle": "Выберите PDF-файл для преобразования", "button": "Выбрать PDF-файл", "loading": "Загрузка библиотеки обработки PDF..."}, "fileInfo": {"title": "Информация о файле", "filename": "Имя файла", "filesize": "Размер файла", "totalPages": "Всего страниц"}, "settings": {"title": "Настройки преобразования", "description": "Настройте выходной формат изображения и качество", "format": "Формат изображения", "formats": {"png": "PNG (без потерь)", "jpeg": "JPEG (с потерями)"}, "scale": "Масштаб ({scale}x)", "scaleDescription": "Больший масштаб дает более четкие, но более крупные изображения", "quality": "Качество JPEG ({quality}%)", "pageSelection": "Выбор страниц", "pageSelectionPlaceholder": "все или 1-3, 5, 7-10", "pageSelectionDescription": "Введите \"all\" для преобразования всех страниц или укажите диапазоны страниц (например, 1-3, 5, 7-10)", "convert": "Начать преобразование", "converting": "Преобразование..."}, "processing": {"title": "Преобразование страниц PDF в изображения..."}, "result": {"title": "Результат преобразования", "description": "Нажмите, чтобы просмотреть или скачать изображения", "downloadAll": "Скачать все", "clear": "Очистить", "download": "Скачать", "page": "Страница {page}"}, "info": {"title": "Инструкции", "items": ["Формат PNG: Сжатие без потерь, подходит для документов и диаграмм", "Формат JPEG: Сжатие с потерями, меньшие файлы, подходит для фотографий", "Масштаб: Контролирует разрешение и четкость выходного изображения", "Выбор страниц поддерживает формат диапазона: 1-3 означает страницы с 1 по 3, разделяйте запятыми", "Вся обработка выполняется в браузере, защищая конфиденциальность файла"]}}, "fbaLabelStamper": {"name": "Штамповщик этикеток FBA", "description": "Бесплатно Amazon FBA этикетка онлайн одним кликом добавить Made in China", "keywords": "этикетка FBA, Amazon FBA, Сделано в Китае, штамповщик PDF, модификатор этикеток, инструменты продавца Amazon, соответствие FBA, маркировка продукции, редактор PDF, логистика Amazon, требования FBA, автоматизация этикеток, китайские товары, маркировка происхождения, торговая площадка Amazon", "title": "Штамповщик этикеток FBA", "subtitle": "Автоматически добавляйте текст \"Made in China\" к этикеткам Amazon FBA, интеллектуально определяя положение \"新品\" или \"New\"", "upload": {"title": "Загрузить PDF-файл", "description": "Выберите PDF-файл этикетки FBA для добавления текста", "button": "Выбрать PDF-файл", "selected": "Выбрано: {filename} ({size} МБ)"}, "textSettings": {"title": "Настройки текста", "description": "Настройте содержимое текста для добавления", "textToAdd": "Текст для добавления", "placeholder": "Сделано в Китае"}, "advancedSettings": {"title": "Расширенные настройки", "description": "Настройте положение и стиль текста", "fontSize": "Размер шрифта (pt)", "xOffset": "Горизонтальное смещение (pt)", "yOffset": "Вертикальное смещение (pt)", "debugMode": "Режим отладки"}, "preview": {"title": "Предварительный просмотр PDF", "description": "Предварительный просмотр вашего PDF-файла", "original": "Исходный PDF", "processed": "Обработанный PDF (со штампом)", "noPreview": "Нет загруженного PDF для предварительного просмотра", "loading": "Загрузка предварительного просмотра...", "error": "Не удалось загрузить предварительный просмотр"}, "process": {"title": "Начать обработку", "description": "Нажмите кнопку, чтобы начать обработку PDF-файла", "button": "Обработать PDF", "processing": "Обработка...", "reset": "Сбросить"}, "download": {"title": "Скачать обработанный PDF", "description": "Скачать PDF с добавленным штампом", "button": "Скачать обработанный PDF", "notReady": "Сначала обработайте PDF, чтобы включить загрузку"}, "status": {"selectFile": "Пожалуйста, сначала выберите PDF-файл", "libraryLoading": "Библиотеки еще не загружены, подождите", "libraryLoaded": "Библиотеки загружены, готовы к обработке файлов", "libraryFailed": "Не удалось загрузить библиотеки, обновите страницу и попробуйте снова", "analyzing": "Анализ текста PDF...", "processing": "Добавление текста...", "generating": "Создание обработанного PDF...", "completed": "Обработка завершена! Обработан<PERSON> {count} этикеток, предварительный просмотр обновлен обработанным PDF.", "failed": "Обработка не удалась: {error}", "previewUpdated": "Предварительный просмотр обновлен обработанным PDF"}, "info": {"title": "Инструкции", "items": ["Загрузите PDF-файл этикетки FBA, содержащий текст \"新品\" или \"New\"", "Инструмент автоматически найдет позицию текста \"新品\", если не найдет, попытается найти \"New\"", "Добавьте указанный текст после целевого текста (по умолчанию: \"Made in China\")", "Настройте размер текста, смещение позиции и другие параметры", "Включите режим отладки, чтобы просмотреть распознанные области текста", "Просмотрите и скачайте измененный PDF-файл после обработки"]}}, "passwordGenerator": {"name": "Генератор паролей", "description": "Генерируйте безопасные пароли с настраиваемыми правилами", "keywords": "генератор паролей, безопасный пароль, случайный пароль, надежный пароль, создатель паролей, генератор паролей, кибербезопасность, стойкость пароля, безопасный вход, политика паролей, генератор случайных строк, аутентификация, безопасность паролей", "title": "Генератор паролей", "subtitle": "Генерируйте надежные и безопасные пароли с настраиваемыми правилами и опциями", "settings": {"title": "Настройки пароля", "description": "Настройте правила и опции генерации пароля", "length": "<PERSON><PERSON><PERSON>на пароля", "lengthDescription": "Рекомендуется: 12-16 символов для надежной безопасности", "includeUppercase": "Включать прописные буквы (A-Z)", "includeLowercase": "Включать строчные буквы (a-z)", "includeNumbers": "Включать цифры (0-9)", "includeSymbols": "Включать символы (!@#$%^&*)", "excludeSimilar": "Исключать похожие символы (0, O, l, I)", "excludeAmbiguous": "Исключать неоднозначные символы ({}, [], (), /\\, ~, `, и т.д.)", "customCharacters": "Пользовательские символы", "customCharactersPlaceholder": "Введите пользовательские символы для включения...", "generate": "Сгенерировать пароль", "generateMultiple": "Сгенерировать несколько ({count})"}, "result": {"title": "Сгенерированный пароль", "multipleTitle": "Сгенерированные пароли ({count})", "strength": "Стойкость пароля", "strengthLevels": {"weak": "Слабый", "fair": "Удовлетворительный", "good": "Хоро<PERSON><PERSON>", "strong": "Надежный", "veryStrong": "Очень надежный"}, "copy": "Копировать", "copied": "Скопировано!", "copyAll": "Копировать все", "regenerate": "Перегенерировать", "clear": "Очистить"}, "analysis": {"title": "Ана<PERSON>из пароля", "length": "Длина: {length} символов", "entropy": "Энтропия: {entropy} бит", "timeToCrack": "Время взлома: {time}", "timeToCrackValues": {"instant": "Мгновенно", "seconds": "{count} секунд", "minutes": "{count} минут", "hours": "{count} ч<PERSON><PERSON><PERSON>", "days": "{count} д<PERSON><PERSON><PERSON>", "months": "{count} месяцев", "years": "{count} лет", "centuries": "{count} веков"}}, "info": {"title": "Советы по безопасности", "items": ["<strong>Д<PERSON>ина имеет значение:</strong> Более длинные пароли экспоненциально сложнее взломать", "<strong>Используйте разнообразие:</strong> Включайте прописные, строчные буквы, цифры и символы", "<strong>Избегайте шаблонов:</strong> Не используйте словарные слова, личную информацию или предсказуемые шаблоны", "<strong>Уникальные пароли:</strong> Используйте разные пароли для разных учетных записей", "<strong>Менеджер паролей:</strong> Рассмотрите возможность использования менеджера паролей для хранения сложных паролей", "<strong>Регулярные обновления:</strong> Регулярно меняйте пароли, особенно для важных учетных записей"]}}, "urlEncoder": {"name": "Кодировщик/декодировщик URL", "description": "Кодируйте и декодируйте URL-адреса и компоненты URL", "keywords": "кодировщик URL, декодировщик URL, процентное кодирование, экранирование URL, кодирование URI, кодировщик параметров запроса, кодировщик Base64, веб-разработка, параметры API, безопасное кодирование URL, кодировка символов, веб-инструменты", "title": "Кодировщик/декодировщик URL", "subtitle": "Кодируйте и декодируйте URL-адреса, параметры запроса и компоненты URL с поддержкой различных типов кодирования", "input": {"title": "Входной текст", "description": "Введите текст или URL для кодирования/декодирования", "placeholder": "Введите URL или текст для кодирования/декодирования...", "loadSample": "Загрузить образец", "clear": "Очистить"}, "actions": {"title": "Действия", "description": "Выберите операцию кодирования/декодирования", "encode": "Кодировать", "decode": "Декодировать", "encodeComponent": "Кодировать компонент", "decodeComponent": "Декодировать компонент", "encodeBase64": "Кодировать Base64", "decodeBase64": "Декодировать Base64"}, "result": {"title": "Результат", "description": "Закодированный/декодированный результат", "placeholder": "Результат появится здесь...", "copy": "Копировать", "copied": "Скопировано!", "download": "Скачать"}, "examples": {"title": "Общие примеры", "url": {"title": "Полный URL", "original": "https://example.com/search?q=hello world&lang=en", "encoded": "https%3A//example.com/search%3Fq%3Dhello%20world%26lang%3Den"}, "component": {"title": "Компонент URL", "original": "hello world & special chars!", "encoded": "hello%20world%20%26%20special%20chars%21"}, "query": {"title": "Параметр запроса", "original": "user name with spaces", "encoded": "user%20name%20with%20spaces"}}, "info": {"title": "Руководство по использованию", "items": ["<strong>Кодирование URL:</strong> Преобразует специальные символы в формат с процентным кодированием для безопасной передачи URL", "<strong>Декодирование URL:</strong> Преобразует символы с процентным кодированием обратно в исходный формат", "<strong>Кодирование компонента:</strong> Кодирует только части компонента URL (параметры запроса, сегменты пути)", "<strong>Base64:</strong> Альтернативный метод кодирования для двоичных данных или специальных символов", "<strong>Общие сценарии использования:</strong> Параметры API, данные форм, поисковые запросы, имена файлов в URL", "Вся обработка выполняется в браузере для защиты конфиденциальности"]}}, "uuidGenerator": {"name": "Генер<PERSON><PERSON><PERSON><PERSON> UUID", "description": "Генерируйте различные версии UUID", "keywords": "генера<PERSON><PERSON><PERSON>, генера<PERSON><PERSON><PERSON>, уникальный идентификатор, UUID v1, UUID v4, UUID v7, универсальный уникальный идентификатор, генератор случайных ID, ID базы данных, генератор ключей API, уникальная строка, генератор идентификаторов", "title": "Генер<PERSON><PERSON><PERSON><PERSON> UUID", "subtitle": "Генерируйте различные версии универсальных уникальных идентификаторов (UUID) с пакетной генерацией и настраиваемыми форматами", "generator": {"title": "Генер<PERSON><PERSON><PERSON><PERSON> UUID", "description": "Выберите версию UUID и количество генераций", "version": "Версия UUID", "versions": {"v1": "Версия 1 (на основе временной метки и MAC-адреса)", "v4": "Версия 4 (случ<PERSON><PERSON>ная, рекомендуется)", "v7": "Версия 7 (на основе временной метки, сортируемая)"}, "count": "Количество генераций", "countDescription": "Генерировать несколько UUID одновременно", "format": "Выходной формат", "formats": {"standard": "Стандартный формат (с дефисами)", "compact": "Компактный формат (без дефисов)", "uppercase": "Формат в верхнем регистре", "braces": "Формат в фигурных скобках {UUID}", "quotes": "Формат в кавычках \"UUID\""}, "generate": "Сгенерировать UUID", "generateMultiple": "Сгенерировать {count} UUID"}, "result": {"title": "Сгенерированный UUID", "multipleTitle": "Сгенерированные UUID ({count} элементов)", "copy": "Копировать", "copied": "Скопировано!", "copyAll": "Копировать все", "download": "Скачать", "clear": "Очистить", "regenerate": "Перегенерировать"}, "examples": {"title": "Примеры UUID", "v1": {"title": "UUID v1 (на основе времени)", "description": "Содержит информацию о временной метке и узле, отслеживаемое время генерации", "example": "6ba7b810-9dad-11d1-80b4-00c04fd430c8"}, "v4": {"title": "UUID v4 (случайный)", "description": "Полностью случайная генерация, наиболее часто используемая версия", "example": "550e8400-e29b-41d4-a716-************"}, "v7": {"title": "UUID v7 (упорядоченный по времени)", "description": "На основе временной метки, поддерживает сортировку по времени", "example": "01890a5d-ac96-774b-bcce-b302099a8057"}}, "validator": {"title": "Валида<PERSON><PERSON>р UUID", "description": "Проверьте правильность формата UUID", "input": "Входной UUID", "placeholder": "Введите UUID для проверки...", "validate": "Проверить", "valid": "Действительный UUID", "invalid": "Недействительный UUID", "details": {"version": "Версия: {version}", "variant": "Вариант: {variant}", "timestamp": "Временная метка: {timestamp}", "node": "Узел: {node}"}}, "info": {"title": "Информация о UUID", "items": ["<strong>UUID v1:</strong> На основе временной метки и MAC-адреса, отслеживаемое время и место генерации", "<strong>UUID v4:</strong> Полностью случайная генерация, наиболее часто используемая, высокая безопасность", "<strong>UUID v7:</strong> На основе временной метки, поддерживает сортировку по времени, подходит для первичных ключей базы данных", "<strong>Уникальность:</strong> UUID глобально уникальны с чрезвычайно низкой вероятностью дублирования", "<strong>Формат:</strong> Стандартный формат - 8-4-4-4-12 шестнадцатеричных символов", "Вся генерация выполняется в браузере, защищая конфиденциальность и безопасность"]}}, "timestampConverter": {"name": "Конвертер временных меток", "description": "Конвертируйте между временными метками и удобочитаемыми датами", "keywords": "конвертер временных меток, временная метка Unix, конвертер эпох, конвертер дат, конвертер времени, временная метка в дату, дата во временную метку, время Unix, конвертер миллисекунд, ISO 8601, RFC 2822, конвертер часовых поясов", "title": "Конвертер временных меток", "subtitle": "Конвертируйте между временными метками Unix и удобочитаемыми датами с поддержкой часовых поясов", "current": {"title": "Текущее время", "description": "Текущая временная метка и дата", "timestamp": "Текущая временная метка", "date": "Текущая дата", "timezone": "Часовой пояс"}, "converter": {"title": "Конвертер временных меток", "description": "Конвертируйте между форматами временных меток и дат", "timestampToDate": "Временная метка в дату", "dateToTimestamp": "Дата во временную метку", "timestampInput": "Введите временную метку", "timestampPlaceholder": "1640995200", "dateInput": "Выберите дату и время", "convert": "Конвертировать", "result": "Результат", "copy": "Копировать", "copied": "Скопировано!", "clear": "Очистить"}, "formats": {"title": "Общие форматы", "description": "Временная метка в разных форматах", "seconds": "Секунды (Unix)", "milliseconds": "Миллисекунды (JavaScript)", "iso": "ISO 8601", "rfc": "RFC 2822", "relative": "Относительное время"}, "batch": {"title": "Пакетная конвертация", "description": "Конвертируйте несколько временных меток одновременно", "input": "Введите временные метки (по одной на строку)", "placeholder": "1640995200\n1641081600\n1641168000", "convertAll": "Конвертировать все", "results": "Результаты конвертации", "download": "Скачать CSV"}, "examples": {"title": "Общие примеры", "now": {"title": "Текущее время", "timestamp": "Текущая временная метка", "date": "Текущая дата"}, "epoch": {"title": "Эпоха Unix", "timestamp": "0", "date": "1 января 1970 00:00:00 UTC"}, "y2k": {"title": "2000 год", "timestamp": "946684800", "date": "1 января 2000 00:00:00 UTC"}}, "info": {"title": "Руководство по использованию", "items": ["<strong>Временная метка Unix:</strong> Секунды с 1 января 1970 00:00:00 UTC", "<strong>Временная метка JavaScript:</strong> Миллисекунды с начала эпохи Unix", "<strong>Поддержка часовых поясов:</strong> Преобразование в разные часовые пояса", "<strong>Пакетная обработка:</strong> Преобразование нескольких временных меток одновременно", "<strong>Общие форматы:</strong> ISO 8601, RFC 2822, относительное время", "Вся обработка выполняется в браузере для защиты конфиденциальности"]}}, "unitConverter": {"name": "Конвертер единиц измерения", "description": "Конвертируйте между различными единицами измерения", "keywords": "конвертер единиц измерения, конвертер измерений, конвертер длины, конвертер веса, конвертер температуры, конвертер площади, конвертер объема, метрический конвертер, имперский конвертер, кулинарный конвертер, конвертер расстояния, конвертер скорости", "title": "Конвертер единиц измерения", "subtitle": "Конвертируйте между различными единицами измерения, включая длину, вес, температуру, площадь, объем и многое другое", "categories": {"length": "Длина", "weight": "<PERSON>е<PERSON>", "temperature": "Температура", "area": "Площадь", "volume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "Время", "speed": "Скорость", "energy": "Энергия"}, "units": {"length": {"mm": "Мил<PERSON>и<PERSON><PERSON><PERSON><PERSON> (мм)", "cm": "Сантиметр (см)", "m": "Метр (м)", "km": "Кило<PERSON><PERSON><PERSON><PERSON> (км)", "in": "Дюйм (дюйм)", "ft": "Фут (фут)", "yd": "Ярд (ярд)", "mi": "Миля (миля)"}, "weight": {"mg": "Миллигра<PERSON>м (мг)", "g": "Грамм (г)", "kg": "Килограмм (кг)", "t": "То<PERSON><PERSON> (т)", "oz": "Унция (унция)", "lb": "Фунт (фунт)", "st": "Стоун (стоун)"}, "temperature": {"c": "Цельсий (°C)", "f": "Фаренгейт (°F)", "k": "К<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (K)", "r": "Ранкин (°R)"}, "area": {"mm2": "Квадратный миллиметр (мм²)", "cm2": "Квадратный сантиметр (см²)", "m2": "Квадратный метр (м²)", "km2": "Квадратный километр (км²)", "in2": "Квадратный дюйм (дюйм²)", "ft2": "Квадратный фут (фут²)", "yd2": "Квадратный ярд (ярд²)", "mi2": "Квадратная миля (миля²)", "acre": "<PERSON><PERSON><PERSON>", "ha": "<PERSON>е<PERSON><PERSON><PERSON><PERSON> (га)"}, "volume": {"ml": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (мл)", "l": "<PERSON><PERSON><PERSON><PERSON> (л)", "m3": "Кубический метр (м³)", "in3": "Кубический дюйм (дюйм³)", "ft3": "Кубический фут (фут³)", "gal": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> (гал)", "qt": "Кварта (кв)", "pt": "Пинта (пинта)", "cup": "Чашка", "fl_oz": "Жидкая унция (жид. унц.)"}, "time": {"ms": "Миллисекунда (мс)", "s": "Секунда (с)", "min": "Минута (мин)", "h": "Час (ч)", "d": "День (дн)", "w": "Неделя (нед)", "mo": "Месяц (мес)", "y": "Год (г)"}, "speed": {"mps": "Метр в секунду (м/с)", "kph": "Километр в час (км/ч)", "mph": "Миля в час (миль/ч)", "fps": "Фут в секунду (фут/с)", "knot": "Узел"}, "energy": {"j": "Джоуль (Дж)", "kj": "Килоджоуль (кДж)", "cal": "Калория (кал)", "kcal": "Килокалория (ккал)", "wh": "Ватт-час (Вт·ч)", "kwh": "Киловатт-час (кВт·ч)", "btu": "Британская тепловая единица (БТЕ)"}}, "converter": {"title": "Преобразование единиц измерения", "description": "Выберите категорию и единицы для преобразования", "category": "Категория преобразования", "fromUnit": "Из", "toUnit": "В", "fromValue": "Входное значение", "toValue": "Результат преобразования", "fromPlaceholder": "Введите значение для преобразования...", "swap": "Поменять единицы", "clear": "Очистить", "copy": "Скопировать результат", "copied": "Скопировано!"}, "quickConvert": {"title": "Быстрое преобразование", "description": "Общие преобразования единиц измерения", "common": [{"name": "Сантиметры в дюймы", "category": "length", "from": "cm", "to": "in", "value": "30"}, {"name": "Килограммы в фунты", "category": "weight", "from": "kg", "to": "lb", "value": "70"}, {"name": "Цельсий в Фаренгейт", "category": "temperature", "from": "c", "to": "f", "value": "25"}, {"name": "Литры в галлоны", "category": "volume", "from": "l", "to": "gal", "value": "10"}]}, "examples": {"title": "Общие применения", "cooking": {"title": "Кулинарные измерения", "description": "Преобразование единиц веса и объема в рецептах"}, "travel": {"title": "Планирование путешествий", "description": "Преобразование единиц расстояния, скорости и температуры"}, "construction": {"title": "Строительство", "description": "Преобразование единиц длины, площади и объема"}, "science": {"title": "Научные расчеты", "description": "Преобразование различных единиц физических величин"}}, "info": {"title": "Руководство по использованию", "items": ["Поддерживает 8 категорий преобразования единиц измерения: длина, вес, температура, площадь, объем, время, скорость, энергия", "Предоставляет шаблоны быстрого преобразования для общих единиц", "Поддерживает высокоточные вычисления с соответствующим количеством знаков после запятой", "Преобразование температуры поддерживает Цельсий, Фар<PERSON>нгейт, Кельвин и Ранкин", "Все вычисления выполняются в браузере, защищая конфиденциальность", "Поддерживает копирование результатов преобразования и обмен единицами"]}}, "hashCalculator": {"name": "Калькулятор хешей", "description": "Вычисляйте хеш-значения для файлов или текста", "keywords": "калькуля<PERSON><PERSON><PERSON> хешей, генератор MD5, генератор SHA1, генератор SHA256, генератор SHA512, калькулятор контрольной суммы, целостность файла, проверка хеша, криптографический хеш, контрольная сумма файла, целостность данных, хеш безопасности", "title": "Калькулятор хешей", "subtitle": "Вычисляйте хеш-значения MD5, SHA1, SHA256 и другие для файлов или текста, используемые для проверки файлов и проверки безопасности", "input": {"title": "Входные данные", "description": "Выберите тип ввода и предоставьте данные", "type": "Тип ввода", "types": {"text": "Текстовый ввод", "file": "Загрузка файла"}, "textPlaceholder": "Введите текст для вычисления хеша...", "fileUpload": "Перетащите файлы сюда или нажмите, чтобы выбрать", "fileButton": "Выбрать файл", "loadSample": "Загрузить образец", "clear": "Очистить"}, "algorithms": {"title": "Алгоритмы хеширования", "description": "Выберите алгоритмы хеширования для использования", "md5": "MD5 (128-бит)", "sha1": "SHA-1 (160-бит)", "sha256": "SHA-256 (256-бит)", "sha512": "SHA-512 (512-бит)", "selectAll": "Выбрать все", "deselectAll": "Снять выделение со всех"}, "calculate": "Вычислить хеш", "calculating": "Вычисление...", "result": {"title": "Результаты хеширования", "description": "Вычисленные хеш-значения", "algorithm": "Алгоритм", "hash": "Хеш-значение", "copy": "Копировать", "copied": "Скопировано!", "copyAll": "Скопировать все", "download": "Скачать результаты", "clear": "Очистить результаты"}, "fileInfo": {"title": "Информация о файле", "name": "Имя файла", "size": "Размер файла", "type": "Тип файла", "lastModified": "Последнее изменение"}, "verification": {"title": "Проверка хеша", "description": "Введите известное хеш-значение для проверки", "input": "Введите известный хеш", "placeholder": "Введите хеш-значение для проверки...", "verify": "Проверить", "match": "✓ Хеш-значения совпадают", "noMatch": "✗ Хеш-значения не совпадают", "selectHash": "Выберите алгоритм хеширования для проверки"}, "examples": {"title": "Общие применения", "fileIntegrity": {"title": "Проверка целостности файла", "description": "Проверьте, что загруженные файлы целы и не изменены"}, "passwordSecurity": {"title": "Хранение паролей с защитой", "description": "Преобразуйте пароли в хеш-значения для безопасного хранения"}, "dataVerification": {"title": "Проверка согласованности данных", "description": "Убедитесь, что данные не были изменены во время передачи"}}, "info": {"title": "Информация об алгоритмах", "items": ["<strong>MD5:</strong> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, но менее безопасный, подходит для контрольных сумм без требований к безопасности", "<strong>SHA-1:</strong> Более безопасный, чем MD5, но больше не рекомендуется для приложений безопасности", "<strong>SHA-256:</strong> В настоящее время широко используемый безопасный алгоритм хеширования, рекомендуется", "<strong>SHA-512:</strong> Алгоритм хеширования с более высоким уровнем безопасности, подходит для требований высокой безопасности", "<strong>Использование:</strong> Проверка файлов, хранение паролей, цифровые подписи, блокчейн и т.д.", "Все вычисления выполняются в браузере, защищая конфиденциальность файлов и данных"]}}}}