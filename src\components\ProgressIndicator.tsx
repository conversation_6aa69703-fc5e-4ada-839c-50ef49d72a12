'use client';

import { cn } from '@/lib/utils';

interface ProgressStep {
  id: string;
  label: string;
  completed: boolean;
  active: boolean;
}

interface ProgressIndicatorProps {
  steps: ProgressStep[];
  className?: string;
}

export function ProgressIndicator({ steps, className }: ProgressIndicatorProps) {
  return (
    <div className={cn('w-full', className)}>
      {/* 桌面端 - 水平布局 */}
      <div className="hidden md:flex items-center justify-center space-x-8">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            {/* 步骤圆点 */}
            <div className="flex items-center">
              <div
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200',
                  step.completed
                    ? 'bg-primary-500 text-white'
                    : step.active
                    ? 'bg-primary-100 text-primary-600 border-2 border-primary-500'
                    : 'bg-gray-100 text-gray-400'
                )}
              >
                {step.completed ? '✓' : index + 1}
              </div>
              
              {/* 步骤标签 */}
              <span
                className={cn(
                  'ml-2 text-sm font-medium transition-colors duration-200',
                  step.completed || step.active
                    ? 'text-gray-900'
                    : 'text-gray-400'
                )}
              >
                {step.label}
              </span>
            </div>

            {/* 连接线 */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  'w-16 h-0.5 mx-4 transition-colors duration-200',
                  step.completed
                    ? 'bg-primary-500'
                    : 'bg-gray-200'
                )}
              />
            )}
          </div>
        ))}
      </div>

      {/* 移动端 - 简化的点状指示器 */}
      <div className="md:hidden flex items-center justify-center space-x-2">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={cn(
              'w-2 h-2 rounded-full transition-all duration-200',
              step.completed
                ? 'bg-primary-500'
                : step.active
                ? 'bg-primary-300'
                : 'bg-gray-200'
            )}
          />
        ))}
      </div>
    </div>
  );
}

interface ProcessingProgressProps {
  progress: number;
  status: string;
  estimatedTime?: number;
  currentPage?: number;
  totalPages?: number;
  className?: string;
}

export function ProcessingProgress({
  progress,
  status,
  estimatedTime,
  currentPage,
  totalPages,
  className
}: ProcessingProgressProps) {
  return (
    <div className={cn('w-full space-y-3', className)}>
      {/* 状态文本 */}
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-700 font-medium">{status}</span>
        <span className="text-gray-500">{Math.round(progress)}%</span>
      </div>

      {/* 进度条 */}
      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
        <div
          className="bg-primary-500 h-full rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* 详细信息 */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        {currentPage && totalPages && (
          <span>已处理 {currentPage}/{totalPages} 页</span>
        )}
        {estimatedTime && estimatedTime > 0 && (
          <span>预计剩余时间: {estimatedTime}秒</span>
        )}
      </div>
    </div>
  );
}
