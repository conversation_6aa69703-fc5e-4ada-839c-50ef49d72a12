"use client";

'use client';

import { useState, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { TopNavigation } from '@/components/TopNavigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Copy, RefreshCw, Eye, EyeOff, Shield, AlertTriangle, CheckCircle } from 'lucide-react';

interface PasswordSettings {
  length: number;
  includeUppercase: boolean;
  includeLowercase: boolean;
  includeNumbers: boolean;
  includeSymbols: boolean;
  excludeSimilar: boolean;
  excludeAmbiguous: boolean;
  customCharacters: string;
}

interface GeneratedPassword {
  password: string;
  strength: 'weak' | 'fair' | 'good' | 'strong' | 'veryStrong';
  entropy: number;
  timeToCrack: string;
}

const DEFAULT_SETTINGS: PasswordSettings = {
  length: 12,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSymbols: true,
  excludeSimilar: false,
  excludeAmbiguous: false,
  customCharacters: ''
};

export default function PasswordGeneratorClient() {
  const t = useTranslations('tools.passwordGenerator');
  const tCommon = useTranslations('common');
  
  const [settings, setSettings] = useState<PasswordSettings>(DEFAULT_SETTINGS);
  const [passwords, setPasswords] = useState<GeneratedPassword[]>([]);
  const [showPasswords, setShowPasswords] = useState(true);
  const [multipleCount, setMultipleCount] = useState(5);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const getCharacterSet = useCallback((settings: PasswordSettings): string => {
    let charset = '';
    
    if (settings.includeLowercase) {
      charset += 'abcdefghijklmnopqrstuvwxyz';
    }
    if (settings.includeUppercase) {
      charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    }
    if (settings.includeNumbers) {
      charset += '0123456789';
    }
    if (settings.includeSymbols) {
      charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }
    if (settings.customCharacters) {
      charset += settings.customCharacters;
    }

    // Remove similar characters if requested
    if (settings.excludeSimilar) {
      charset = charset.replace(/[0Ol1]/g, '');
    }
    
    // Remove ambiguous characters if requested
    if (settings.excludeAmbiguous) {
      charset = charset.replace(/[{}[\]()\/\\~`,]/g, '');
    }

    return charset;
  }, []);

  const calculatePasswordStrength = useCallback((password: string): { strength: GeneratedPassword['strength'], entropy: number, timeToCrack: string } => {
    const charset = getCharacterSet(settings);
    const entropy = Math.log2(Math.pow(charset.length, password.length));
    
    let strength: GeneratedPassword['strength'] = 'weak';
    if (entropy >= 60) strength = 'veryStrong';
    else if (entropy >= 50) strength = 'strong';
    else if (entropy >= 40) strength = 'good';
    else if (entropy >= 30) strength = 'fair';

    // Calculate time to crack (simplified)
    const combinations = Math.pow(charset.length, password.length);
    const secondsToCrack = combinations / (1000000000); // Assume 1B attempts per second
    
    let timeToCrack = '';
    if (secondsToCrack < 1) {
      timeToCrack = t('analysis.timeToCrackValues.instant');
    } else if (secondsToCrack < 60) {
      timeToCrack = t('analysis.timeToCrackValues.seconds', { count: Math.round(secondsToCrack) });
    } else if (secondsToCrack < 3600) {
      timeToCrack = t('analysis.timeToCrackValues.minutes', { count: Math.round(secondsToCrack / 60) });
    } else if (secondsToCrack < 86400) {
      timeToCrack = t('analysis.timeToCrackValues.hours', { count: Math.round(secondsToCrack / 3600) });
    } else if (secondsToCrack < 31536000) {
      timeToCrack = t('analysis.timeToCrackValues.days', { count: Math.round(secondsToCrack / 86400) });
    } else if (secondsToCrack < 31536000 * 100) {
      timeToCrack = t('analysis.timeToCrackValues.years', { count: Math.round(secondsToCrack / 31536000) });
    } else {
      timeToCrack = t('analysis.timeToCrackValues.centuries', { count: Math.round(secondsToCrack / (31536000 * 100)) });
    }

    return { strength, entropy: Math.round(entropy), timeToCrack };
  }, [settings, getCharacterSet, t]);

  const generatePassword = useCallback((length: number): string => {
    const charset = getCharacterSet(settings);
    if (!charset) return '';

    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }, [settings, getCharacterSet]);

  const handleGenerate = useCallback((multiple: boolean = false) => {
    const count = multiple ? multipleCount : 1;
    const newPasswords: GeneratedPassword[] = [];

    for (let i = 0; i < count; i++) {
      const password = generatePassword(settings.length);
      const analysis = calculatePasswordStrength(password);
      newPasswords.push({
        password,
        ...analysis
      });
    }

    setPasswords(newPasswords);
    setCopiedIndex(null);
  }, [settings.length, multipleCount, generatePassword, calculatePasswordStrength]);

  const copyToClipboard = useCallback(async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  }, []);

  const copyAllPasswords = useCallback(async () => {
    const allPasswords = passwords.map(p => p.password).join('\n');
    try {
      await navigator.clipboard.writeText(allPasswords);
      setCopiedIndex(-1); // Special index for "copy all"
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy all:', err);
    }
  }, [passwords]);

  const getStrengthColor = (strength: GeneratedPassword['strength']) => {
    switch (strength) {
      case 'weak': return 'text-red-600';
      case 'fair': return 'text-orange-600';
      case 'good': return 'text-yellow-600';
      case 'strong': return 'text-blue-600';
      case 'veryStrong': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStrengthIcon = (strength: GeneratedPassword['strength']) => {
    switch (strength) {
      case 'weak': return <AlertTriangle className="h-4 w-4" />;
      case 'fair': return <AlertTriangle className="h-4 w-4" />;
      case 'good': return <Shield className="h-4 w-4" />;
      case 'strong': return <Shield className="h-4 w-4" />;
      case 'veryStrong': return <CheckCircle className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />
      <main className="p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Settings Panel */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>{t('settings.title')}</span>
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">{t('settings.description')}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Password Length */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('settings.length')}: {settings.length}
                    </label>
                    <Input
                      type="range"
                      min="4"
                      max="128"
                      value={settings.length}
                      onChange={(e) => setSettings(prev => ({ ...prev, length: parseInt(e.target.value) }))}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground mt-1">{t('settings.lengthDescription')}</p>
                  </div>

                  {/* Character Type Options */}
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.includeUppercase}
                        onChange={(e) => setSettings(prev => ({ ...prev, includeUppercase: e.target.checked }))}
                        className="rounded"
                      />
                      <span className="text-sm">{t('settings.includeUppercase')}</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.includeLowercase}
                        onChange={(e) => setSettings(prev => ({ ...prev, includeLowercase: e.target.checked }))}
                        className="rounded"
                      />
                      <span className="text-sm">{t('settings.includeLowercase')}</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.includeNumbers}
                        onChange={(e) => setSettings(prev => ({ ...prev, includeNumbers: e.target.checked }))}
                        className="rounded"
                      />
                      <span className="text-sm">{t('settings.includeNumbers')}</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.includeSymbols}
                        onChange={(e) => setSettings(prev => ({ ...prev, includeSymbols: e.target.checked }))}
                        className="rounded"
                      />
                      <span className="text-sm">{t('settings.includeSymbols')}</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.excludeSimilar}
                        onChange={(e) => setSettings(prev => ({ ...prev, excludeSimilar: e.target.checked }))}
                        className="rounded"
                      />
                      <span className="text-sm">{t('settings.excludeSimilar')}</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.excludeAmbiguous}
                        onChange={(e) => setSettings(prev => ({ ...prev, excludeAmbiguous: e.target.checked }))}
                        className="rounded"
                      />
                      <span className="text-sm">{t('settings.excludeAmbiguous')}</span>
                    </label>
                  </div>

                  {/* Custom Characters */}
                  <div>
                    <label className="block text-sm font-medium mb-2">{t('settings.customCharacters')}</label>
                    <Input
                      value={settings.customCharacters}
                      onChange={(e) => setSettings(prev => ({ ...prev, customCharacters: e.target.value }))}
                      placeholder={t('settings.customCharactersPlaceholder')}
                    />
                  </div>

                  {/* Generate Buttons */}
                  <div className="space-y-2 pt-4">
                    <Button 
                      onClick={() => handleGenerate(false)} 
                      className="w-full"
                      disabled={!getCharacterSet(settings)}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      {t('settings.generate')}
                    </Button>
                    
                    <div className="flex space-x-2">
                      <Input
                        type="number"
                        min="2"
                        max="50"
                        value={multipleCount}
                        onChange={(e) => setMultipleCount(parseInt(e.target.value) || 5)}
                        className="w-20"
                      />
                      <Button 
                        onClick={() => handleGenerate(true)} 
                        variant="outline"
                        className="flex-1"
                        disabled={!getCharacterSet(settings)}
                      >
                        {t('settings.generateMultiple', { count: multipleCount })}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Results Panel */}
            <div className="lg:col-span-2 space-y-6">
              {passwords.length > 0 && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>
                        {passwords.length === 1 ? t('result.title') : t('result.multipleTitle', { count: passwords.length })}
                      </CardTitle>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowPasswords(!showPasswords)}
                        >
                          {showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                        {passwords.length > 1 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={copyAllPasswords}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            {copiedIndex === -1 ? t('result.copied') : t('result.copyAll')}
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGenerate(passwords.length > 1)}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          {t('result.regenerate')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPasswords([])}
                        >
                          {t('result.clear')}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {passwords.map((passwordData, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className={`flex items-center space-x-1 ${getStrengthColor(passwordData.strength)}`}>
                                {getStrengthIcon(passwordData.strength)}
                                <span className="text-sm font-medium">
                                  {t(`result.strengthLevels.${passwordData.strength}`)}
                                </span>
                              </span>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(passwordData.password, index)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              {copiedIndex === index ? t('result.copied') : t('result.copy')}
                            </Button>
                          </div>
                          
                          <div className="font-mono text-lg bg-muted p-3 rounded border break-all">
                            {showPasswords ? passwordData.password : '•'.repeat(passwordData.password.length)}
                          </div>
                          
                          <div className="mt-2 text-sm text-muted-foreground space-y-1">
                            <div>{t('analysis.length', { length: passwordData.password.length })}</div>
                            <div>{t('analysis.entropy', { entropy: passwordData.entropy })}</div>
                            <div>{t('analysis.timeToCrack', { time: passwordData.timeToCrack })}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Security Tips */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>{t('info.title')}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {(t.raw('info.items') as string[]).map((item, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <div 
                          className="text-sm text-muted-foreground"
                          dangerouslySetInnerHTML={{ __html: item }}
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
