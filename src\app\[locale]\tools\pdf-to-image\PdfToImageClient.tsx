"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { Upload, Download, FileImage, Settings, Info, Image } from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from 'next-intl';

// 动态导入 PDF.js 以避免 SSR 问题
let pdfjsLib: any = null;

interface ConvertedImage {
  pageNumber: number;
  dataUrl: string;
  filename: string;
  size: number;
}

export default function PdfToImageClient() {
  const t = useTranslations('tools.pdfToImage');
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [pageCount, setPageCount] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [convertedImages, setConvertedImages] = useState<ConvertedImage[]>([]);
  const [imageFormat, setImageFormat] = useState<'png' | 'jpeg'>('png');
  const [imageQuality, setImageQuality] = useState<number>(0.9);
  const [scale, setScale] = useState<number>(2);
  const [selectedPages, setSelectedPages] = useState<string>('all');
  const [isLibraryLoaded, setIsLibraryLoaded] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 动态加载 PDF.js
  useEffect(() => {
    const loadPdfJs = async () => {
      try {
        const pdfjs = await import('pdfjs-dist');
        pdfjsLib = pdfjs;

        // 设置 worker
        pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

        setIsLibraryLoaded(true);
      } catch (error) {
        console.error('Failed to load PDF.js:', error);
      }
    };

    loadPdfJs();
  }, []);

  const handleFileSelect = useCallback(async (files: FileList) => {
    if (!isLibraryLoaded || !pdfjsLib) {
      alert('PDF processing library is loading, please try again later');
      return;
    }

    const file = files[0];

    if (!file || file.type !== 'application/pdf') {
      alert('Please select a valid PDF file');
      return;
    }

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      const pages = pdf.numPages;

      setPdfFile(file);
      setPageCount(pages);
      setConvertedImages([]);
    } catch (error) {
      console.error('Failed to read PDF file:', error);
      alert('Unable to read PDF file, please ensure the file is valid');
    }
  }, [isLibraryLoaded]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const parsePageSelection = (): number[] => {
    if (selectedPages === 'all') {
      return Array.from({ length: pageCount }, (_, i) => i + 1);
    }

    const pages: number[] = [];
    const parts = selectedPages.split(',').map(s => s.trim());
    
    for (const part of parts) {
      if (part.includes('-')) {
        const [start, end] = part.split('-').map(s => parseInt(s.trim()));
        if (start && end && start <= end && start >= 1 && end <= pageCount) {
          for (let i = start; i <= end; i++) {
            pages.push(i);
          }
        }
      } else {
        const page = parseInt(part);
        if (page >= 1 && page <= pageCount) {
          pages.push(page);
        }
      }
    }
    
    return [...new Set(pages)].sort((a, b) => a - b);
  };

  const convertToImages = async () => {
    if (!pdfFile || !isLibraryLoaded || !pdfjsLib) {
      alert('PDF processing library not loaded or file not selected');
      return;
    }

    setIsProcessing(true);
    setConvertedImages([]);

    try {
      const arrayBuffer = await pdfFile.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      const pagesToConvert = parsePageSelection();
      const results: ConvertedImage[] = [];

      for (const pageNum of pagesToConvert) {
        try {
          const page = await pdf.getPage(pageNum);
          const viewport = page.getViewport({ scale });
          
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          
          if (!context) continue;
          
          canvas.height = viewport.height;
          canvas.width = viewport.width;

          const renderContext = {
            canvasContext: context,
            viewport: viewport,
          };

          await page.render(renderContext).promise;

          // 转换为指定格式
          const mimeType = imageFormat === 'png' ? 'image/png' : 'image/jpeg';
          const dataUrl = canvas.toDataURL(mimeType, imageQuality);
          
          // 计算文件大小（估算）
          const base64Data = dataUrl.split(',')[1];
          const size = Math.round((base64Data.length * 3) / 4);

          const filename = `${pdfFile.name.replace('.pdf', '')}_page${pageNum}.${imageFormat}`;

          results.push({
            pageNumber: pageNum,
            dataUrl,
            filename,
            size,
          });
        } catch (error) {
          console.error(`Failed to convert page ${pageNum}:`, error);
        }
      }

      setConvertedImages(results);
    } catch (error) {
      console.error('Failed to convert PDF:', error);
      alert('Error occurred while converting PDF, please check file and settings');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadImage = (image: ConvertedImage) => {
    const link = document.createElement('a');
    link.href = image.dataUrl;
    link.download = image.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadAll = () => {
    convertedImages.forEach((image, index) => {
      setTimeout(() => downloadImage(image), index * 100);
    });
  };

  const clearAll = () => {
    setPdfFile(null);
    setPageCount(0);
    setConvertedImages([]);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>

          {/* Upload Area */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">{t('upload.title')}</h3>
                <p className="text-gray-500 mb-4">
                  {t('upload.subtitle')}
                </p>
                {!isLibraryLoaded ? (
                  <div className="text-center">
                    <div className="animate-spin h-6 w-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                    <p className="text-sm text-gray-500">{t('upload.loading')}</p>
                  </div>
                ) : (
                  <Button asChild>
                    <label>
                      {t('upload.button')}
                      <input
                        type="file"
                        accept=".pdf,application/pdf"
                        className="hidden"
                        onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                      />
                    </label>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* File Info */}
          {pdfFile && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileImage className="h-5 w-5" />
                  {t('fileInfo.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">{t('fileInfo.filename')}</p>
                    <p className="font-medium">{pdfFile.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{t('fileInfo.filesize')}</p>
                    <p className="font-medium">{formatFileSize(pdfFile.size)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{t('fileInfo.totalPages')}</p>
                    <p className="font-medium">{pageCount} 页</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Conversion Settings */}
          {pdfFile && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  {t('settings.title')}
                </CardTitle>
                <CardDescription>{t('settings.description')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">{t('settings.format')}</label>
                    <select
                      value={imageFormat}
                      onChange={(e) => setImageFormat(e.target.value as 'png' | 'jpeg')}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="png">{t('settings.formats.png')}</option>
                      <option value="jpeg">{t('settings.formats.jpeg')}</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      {t('settings.scale', { scale })}
                    </label>
                    <Input
                      type="range"
                      min="1"
                      max="4"
                      step="0.5"
                      value={scale}
                      onChange={(e) => setScale(parseFloat(e.target.value))}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {t('settings.scaleDescription')}
                    </div>
                  </div>
                </div>

                {imageFormat === 'jpeg' && (
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      {t('settings.quality', { quality: Math.round(imageQuality * 100) })}
                    </label>
                    <Input
                      type="range"
                      min="0.1"
                      max="1"
                      step="0.1"
                      value={imageQuality}
                      onChange={(e) => setImageQuality(parseFloat(e.target.value))}
                    />
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    {t('settings.pageSelection')}
                  </label>
                  <Input
                    type="text"
                    value={selectedPages}
                    onChange={(e) => setSelectedPages(e.target.value)}
                    placeholder={t('settings.pageSelectionPlaceholder')}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {t('settings.pageSelectionDescription')}
                  </p>
                </div>

                <Button
                  onClick={convertToImages}
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? t('settings.converting') : t('settings.convert')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Processing Status */}
          {isProcessing && (
            <Card className="mb-6">
              <CardContent className="p-6 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>{t('processing.title')}</p>
              </CardContent>
            </Card>
          )}

          {/* Results */}
          {convertedImages.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Image className="h-5 w-5" />
                      {t('result.title')} ({convertedImages.length} 张图片)
                    </CardTitle>
                    <CardDescription>{t('result.description')}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={downloadAll}>
                      {t('result.downloadAll')}
                    </Button>
                    <Button onClick={clearAll} variant="outline">
                      {t('result.clear')}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {convertedImages.map((image) => (
                    <div key={image.pageNumber} className="border rounded-lg p-4 bg-white">
                      <div className="aspect-[3/4] mb-3 bg-gray-100 rounded overflow-hidden">
                        <img
                          src={image.dataUrl}
                          alt={`第 ${image.pageNumber} 页`}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">{image.filename}</h4>
                        <p className="text-xs text-gray-500">
                          第 {image.pageNumber} 页 • {formatFileSize(image.size)}
                        </p>
                        <Button
                          onClick={() => downloadImage(image)}
                          size="sm"
                          className="w-full"
                        >
                          {t('result.download')}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
      
      {/* Hidden canvas for rendering */}
      <canvas ref={canvasRef} style={{ display: 'none' }} />
    </div>
  );
}
