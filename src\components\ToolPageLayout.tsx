import { ReactNode } from 'react';
import { useTranslations } from 'next-intl';
import { Breadcrumb, generateToolBreadcrumbs } from '@/components/Breadcrumb';
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import { Tool, toolCategories } from '@/config/tools';

interface ToolPageLayoutProps {
  children: ReactNode;
  tool: Tool;
  locale: string;
  toolName: string;
  toolDescription: string;
}

export function ToolPageLayout({ 
  children, 
  tool, 
  locale, 
  toolName, 
  toolDescription 
}: ToolPageLayoutProps) {
  const t = useTranslations('navigation.categories');
  
  // 获取分类名称
  const categoryName = t(tool.category as any);
  
  // 生成面包屑
  const breadcrumbs = generateToolBreadcrumbs(toolName, categoryName, locale);
  
  // 生成结构化数据
  const softwareSchema = generateSoftwareApplicationSchema(
    tool, 
    locale, 
    toolName, 
    toolDescription
  );
  
  const breadcrumbSchema = generateBreadcrumbSchema(
    [
      { name: '首页', url: `/${locale}` },
      { name: categoryName, url: `/${locale}#${tool.category}` },
      { name: toolName, url: `/${locale}${tool.path}` }
    ],
    locale
  );

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-6">
          {/* 面包屑导航 */}
          <Breadcrumb 
            items={breadcrumbs} 
            className="mb-6"
          />
          
          {/* 页面标题区域 */}
          <div className="mb-8">
            <h1 className="text-3xl text-center font-bold text-gray-900 mb-2">
              {toolName}
            </h1>
            <p className=" text-center text-lg text-gray-600">
              {toolDescription}
            </p>
          </div>
          
          {/* 工具内容 */}
          {children}
        </div>
      </div>
    </>
  );
}
