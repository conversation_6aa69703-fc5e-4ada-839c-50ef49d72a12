import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import { getTranslations } from 'next-intl/server';
import { getToolById } from '@/config/tools';
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import UuidGeneratorClient from './UuidGeneratorClient';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: 'uuidGenerator',
    path: '/tools/uuid-generator'
  });
}

export default async function UuidGeneratorPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  const t = await getTranslations('tools.uuidGenerator');
  const categoryT = await getTranslations('navigation.categories');
  const structuredDataT = await getTranslations('structuredData');
  const navT = await getTranslations('navigation');
  
  // 获取工具信息
  const tool = getToolById('uuid-generator')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);
  
  // 生成结构化数据
  const softwareSchema = await generateSoftwareApplicationSchema(
    tool,
    locale,
    toolName,
    toolDescription,
    structuredDataT
  );

  const breadcrumbSchema = generateBreadcrumbSchema(
    [
      { name: navT('home'), url: `/${locale}` },
      { name: categoryName, url: `/${locale}#${tool.category}` },
      { name: toolName, url: `/${locale}${tool.path}` }
    ],
    locale
  );

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      <UuidGeneratorClient />
    </>
  );
}
