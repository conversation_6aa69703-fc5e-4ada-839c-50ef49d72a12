"use client";

import { useState, useEffect } from "react";
import { Loader2, AlertCircle, FileText } from "lucide-react";

interface PdfViewerProps {
  pdfUrl?: string | null;
  pdfFile?: File | null;
  pdfBytes?: Uint8Array | null;
  className?: string;
  height?: string;
  loadingText?: string;
  errorText?: string;
  noFileText?: string;
  isProcessed?: boolean;
  originalLabel?: string;
  processedLabel?: string;
}

export function PdfViewer({
  pdfUrl,
  pdfFile,
  pdfBytes,
  className = "",
  height = "400px",
  loadingText = "加载中...",
  errorText = "PDF加载失败",
  noFileText = "未选择PDF文件",
  isProcessed = false,
  originalLabel = "原始PDF",
  processedLabel = "处理后的PDF"
}: PdfViewerProps) {
  const [viewerUrl, setViewerUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let objectUrl: string | null = null;

    const setupViewer = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // 清理之前的URL
        if (viewerUrl && viewerUrl.startsWith('blob:')) {
          URL.revokeObjectURL(viewerUrl);
        }

        if (pdfBytes) {
          // 从Uint8Array创建blob URL
          const blob = new Blob([pdfBytes], { type: 'application/pdf' });
          objectUrl = URL.createObjectURL(blob);
          setViewerUrl(objectUrl);
        } else if (pdfFile) {
          // 从File对象创建blob URL
          objectUrl = URL.createObjectURL(pdfFile);
          setViewerUrl(objectUrl);
        } else if (pdfUrl) {
          // 直接使用提供的URL
          setViewerUrl(pdfUrl);
        } else {
          setViewerUrl(null);
        }
      } catch (err) {
        console.error('PDF查看器设置失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setIsLoading(false);
      }
    };

    setupViewer();

    // 清理函数
    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [pdfUrl, pdfFile, pdfBytes]);

  // 清理viewerUrl
  useEffect(() => {
    return () => {
      if (viewerUrl && viewerUrl.startsWith('blob:')) {
        URL.revokeObjectURL(viewerUrl);
      }
    };
  }, [viewerUrl]);

  if (isLoading) {
    return (
      <div 
        className={`border rounded-lg bg-gray-50 flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">{loadingText}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div 
        className={`border rounded-lg bg-gray-50 flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center text-red-500">
          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">{errorText}</p>
          <p className="text-xs mt-1">{error}</p>
        </div>
      </div>
    );
  }

  if (!viewerUrl) {
    return (
      <div 
        className={`border rounded-lg bg-gray-50 flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center text-muted-foreground">
          <FileText className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">{noFileText}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div 
        className="border rounded-lg overflow-hidden bg-white"
        style={{ height }}
      >
        <iframe
          src={viewerUrl}
          className="w-full h-full"
          title="PDF预览"
          style={{ border: 'none' }}
        />
      </div>
      <p className="text-xs text-center text-muted-foreground">
        {isProcessed ? processedLabel : originalLabel}
      </p>
    </div>
  );
}
