"use client";


import { useState, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { Hash, Copy, Download, RotateCcw, CheckCircle, XCircle, Info } from 'lucide-react';
import { TopNavigation } from "@/components/TopNavigation";

// Simple UUID v4 generator
function generateUUIDv4(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Simple UUID v1 generator (simplified version)
function generateUUIDv1(): string {
  const timestamp = Date.now();
  const timeHex = timestamp.toString(16).padStart(12, '0');
  const randomHex = Math.random().toString(16).substr(2, 8);
  const nodeHex = Math.random().toString(16).substr(2, 12);
  
  return `${timeHex.substr(0, 8)}-${timeHex.substr(8, 4)}-1${timeHex.substr(12, 3)}-${randomHex.substr(0, 4)}-${nodeHex}`;
}

// Simple UUID v7 generator (timestamp-based)
function generateUUIDv7(): string {
  const timestamp = Date.now();
  const timeHex = timestamp.toString(16).padStart(12, '0');
  const randomHex = Math.random().toString(16).substr(2, 16);
  
  return `${timeHex.substr(0, 8)}-${timeHex.substr(8, 4)}-7${randomHex.substr(0, 3)}-${randomHex.substr(3, 4)}-${randomHex.substr(7, 12)}`;
}

function generateUUID(version: string): string {
  switch (version) {
    case 'v1': return generateUUIDv1();
    case 'v4': return generateUUIDv4();
    case 'v7': return generateUUIDv7();
    default: return generateUUIDv4();
  }
}

function formatUUID(uuid: string, format: string): string {
  const cleanUuid = uuid.replace(/-/g, '');
  
  switch (format) {
    case 'standard': return uuid;
    case 'compact': return cleanUuid;
    case 'uppercase': return uuid.toUpperCase();
    case 'braces': return `{${uuid}}`;
    case 'quotes': return `"${uuid}"`;
    default: return uuid;
  }
}

function validateUUID(uuid: string): { valid: boolean; version?: string; details?: any } {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-7][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(uuid)) {
    return { valid: false };
  }
  
  const version = uuid.charAt(14);
  return {
    valid: true,
    version: `v${version}`,
    details: {
      version: `v${version}`,
      variant: 'RFC 4122',
      timestamp: version === '1' || version === '7' ? 'Available' : 'N/A',
      node: version === '1' ? 'Available' : 'N/A'
    }
  };
}

export default function UuidGeneratorClient() {
  const t = useTranslations('tools.uuidGenerator');
  const [version, setVersion] = useState('v4');
  const [count, setCount] = useState(1);
  const [format, setFormat] = useState('standard');
  const [uuids, setUuids] = useState<string[]>([]);
  const [copied, setCopied] = useState(false);
  const [validatorInput, setValidatorInput] = useState('');
  const [validationResult, setValidationResult] = useState<{ valid: boolean; version?: string; details?: any } | null>(null);

  const generateUUIDs = useCallback(() => {
    const newUuids = Array.from({ length: count }, () => {
      const uuid = generateUUID(version);
      return formatUUID(uuid, format);
    });
    setUuids(newUuids);
  }, [version, count, format]);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, []);

  const copyAllUUIDs = useCallback(async () => {
    const text = uuids.join('\n');
    await copyToClipboard(text);
  }, [uuids, copyToClipboard]);

  const downloadUUIDs = useCallback(() => {
    if (uuids.length === 0) return;
    
    const text = uuids.join('\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `uuids-${version}-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [uuids, version]);

  const validateUUIDInput = useCallback(() => {
    if (!validatorInput.trim()) {
      setValidationResult(null);
      return;
    }
    
    const result = validateUUID(validatorInput.trim());
    setValidationResult(result);
  }, [validatorInput]);

  const clearAll = useCallback(() => {
    setUuids([]);
    setValidatorInput('');
    setValidationResult(null);
  }, []);

  const loadExample = useCallback((exampleVersion: string) => {
    setVersion(exampleVersion);
    setCount(1);
    setFormat('standard');
    const uuid = generateUUID(exampleVersion);
    setUuids([uuid]);
  }, []);

  return (
   <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Examples and Info */}
        <div className="lg:col-span-1 space-y-6">
          {/* Examples */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('examples.title')}</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.v1.title')}</h3>
                <p className="text-sm text-gray-600 mb-2">{t('examples.v1.description')}</p>
                <button
                  onClick={() => loadExample('v1')}
                  className="text-xs text-blue-600 hover:text-blue-800 font-mono break-all"
                >
                  {t('examples.v1.example')}
                </button>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.v4.title')}</h3>
                <p className="text-sm text-gray-600 mb-2">{t('examples.v4.description')}</p>
                <button
                  onClick={() => loadExample('v4')}
                  className="text-xs text-green-600 hover:text-green-800 font-mono break-all"
                >
                  {t('examples.v4.example')}
                </button>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.v7.title')}</h3>
                <p className="text-sm text-gray-600 mb-2">{t('examples.v7.description')}</p>
                <button
                  onClick={() => loadExample('v7')}
                  className="text-xs text-purple-600 hover:text-purple-800 font-mono break-all"
                >
                  {t('examples.v7.example')}
                </button>
              </div>
            </div>
          </div>

          {/* Info */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Info className="w-5 h-5 mr-2" />
              {t('info.title')}
            </h2>
            <ul className="space-y-2">
              {t.raw('info.items').map((item: string, index: number) => (
                <li key={index} className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: item }} />
              ))}
            </ul>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Generator */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('generator.title')}</h2>
              <button
                onClick={clearAll}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>
            <p className="text-gray-600 mb-6">{t('generator.description')}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {/* Version Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('generator.version')}
                </label>
                <select
                  value={version}
                  onChange={(e) => setVersion(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="v1">{t('generator.versions.v1')}</option>
                  <option value="v4">{t('generator.versions.v4')}</option>
                  <option value="v7">{t('generator.versions.v7')}</option>
                </select>
              </div>

              {/* Count */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('generator.count')}
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={count}
                  onChange={(e) => setCount(Math.max(1, Math.min(100, parseInt(e.target.value) || 1)))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">{t('generator.countDescription')}</p>
              </div>

              {/* Format */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('generator.format')}
                </label>
                <select
                  value={format}
                  onChange={(e) => setFormat(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="standard">{t('generator.formats.standard')}</option>
                  <option value="compact">{t('generator.formats.compact')}</option>
                  <option value="uppercase">{t('generator.formats.uppercase')}</option>
                  <option value="braces">{t('generator.formats.braces')}</option>
                  <option value="quotes">{t('generator.formats.quotes')}</option>
                </select>
              </div>
            </div>

            <button
              onClick={generateUUIDs}
              className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Hash className="w-5 h-5 mr-2 inline" />
              {count === 1 ? t('generator.generate') : t('generator.generateMultiple', { count })}
            </button>
          </div>

          {/* Results */}
          {uuids.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  {uuids.length === 1 ? t('result.title') : t('result.multipleTitle', { count: uuids.length })}
                </h2>
                <div className="flex items-center space-x-2">
                  {uuids.length > 1 && (
                    <button
                      onClick={copyAllUUIDs}
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                    >
                      {copied ? t('result.copied') : t('result.copyAll')}
                    </button>
                  )}
                  <button
                    onClick={downloadUUIDs}
                    className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-1 inline" />
                    {t('result.download')}
                  </button>
                  <button
                    onClick={generateUUIDs}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                  >
                    <RotateCcw className="w-4 h-4 mr-1 inline" />
                    {t('result.regenerate')}
                  </button>
                </div>
              </div>

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {uuids.map((uuid, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded border">
                    <code className="flex-1 text-sm font-mono text-gray-900 break-all">
                      {uuid}
                    </code>
                    <button
                      onClick={() => copyToClipboard(uuid)}
                      className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* UUID Validator */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('validator.title')}</h2>
            <p className="text-gray-600 mb-4">{t('validator.description')}</p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('validator.input')}
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={validatorInput}
                    onChange={(e) => setValidatorInput(e.target.value)}
                    placeholder={t('validator.placeholder')}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={validateUUIDInput}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {t('validator.validate')}
                  </button>
                </div>
              </div>

              {validationResult && (
                <div className={`p-4 rounded-lg border ${
                  validationResult.valid
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center mb-2">
                    {validationResult.valid ? (
                      <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600 mr-2" />
                    )}
                    <span className={`font-medium ${
                      validationResult.valid ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {validationResult.valid ? t('validator.valid') : t('validator.invalid')}
                    </span>
                  </div>

                  {validationResult.valid && validationResult.details && (
                    <div className="text-sm text-green-700 space-y-1">
                      <div>{t('validator.details.version', { version: validationResult.details.version })}</div>
                      <div>{t('validator.details.variant', { variant: validationResult.details.variant })}</div>
                      <div>{t('validator.details.timestamp', { timestamp: validationResult.details.timestamp })}</div>
                      <div>{t('validator.details.node', { node: validationResult.details.node })}</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
    </main>
    </div>
  );
}
