"use client";

import Link from "next/link";
import { usePathname, useParams } from "next/navigation";
import { useState } from "react";
import { useTranslations } from 'next-intl';

import {
  Menu,
  X,
  Home,
  Image,
  FileText,
  Database,
  Wrench,
  FileType,
  Search,
  Globe
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toolCategories, searchTools, getToolsByCategory } from "@/config/tools";
import { cn } from "@/lib/utils";
import { locales, localeLabels, type Locale } from "@/i18n/config";

const categoryIcons = {
  image: Image,
  text: FileText,
  data: Database,
  utility: Wrench,
  pdf: FileType,
};

export function Navigation() {
  const pathname = usePathname();
  const params = useParams();
  const locale = params.locale as Locale;
  const t = useTranslations('navigation');
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showSearch, setShowSearch] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const results = searchTools(query);
      setSearchResults(results);
      setShowSearch(true);
    } else {
      setShowSearch(false);
    }
  };

  const switchLanguage = (newLocale: Locale) => {
    const currentPath = pathname.replace(`/${locale}`, '');
    window.location.href = `/${newLocale}${currentPath}`;
  };

  return (
    <>
      {/* 移动端菜单按钮 */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* 侧边栏 */}
      <nav
        className={cn(
          "fixed left-0 top-0 z-40 h-full w-80 bg-background border-r transform transition-transform duration-300 ease-in-out overflow-y-auto",
          isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}
      >
        <div className="p-6">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2 mb-6">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold">A</span>
            </div>
            <span className="text-xl font-bold">{t('title')}</span>
          </Link>

          {/* Language Switcher */}
          <div className="relative mb-6">
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-between"
              onClick={() => setShowLanguageMenu(!showLanguageMenu)}
            >
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4" />
                <span>{localeLabels[locale]}</span>
              </div>
            </Button>

            {showLanguageMenu && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50">
                {locales.map((loc) => (
                  <button
                    key={loc}
                    onClick={() => {
                      switchLanguage(loc);
                      setShowLanguageMenu(false);
                    }}
                    className={cn(
                      "w-full text-left px-4 py-2 hover:bg-accent text-sm",
                      loc === locale && "bg-accent"
                    )}
                  >
                    {localeLabels[loc]}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* 搜索框 */}
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('search')}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
            
            {/* 搜索结果 */}
            {showSearch && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                {searchResults.length > 0 ? (
                  searchResults.map((tool) => (
                    <Link
                      key={tool.id}
                      href={`/${locale}${tool.path}`}
                      className="block px-4 py-2 hover:bg-accent text-sm"
                      onClick={() => {
                        setShowSearch(false);
                        setSearchQuery("");
                        setIsOpen(false);
                      }}
                    >
                      <div className="font-medium">{tool.name}</div>
                      <div className="text-muted-foreground text-xs">{tool.description}</div>
                    </Link>
                  ))
                ) : (
                  <div className="px-4 py-2 text-sm text-muted-foreground">
                    {locale === 'zh' ? '未找到相关工具' : 'No tools found'}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 首页链接 */}
          <Link
            href={`/${locale}`}
            className={cn(
              "flex items-center space-x-3 px-3 py-2 rounded-lg mb-4 transition-colors",
              pathname === `/${locale}`
                ? "bg-primary text-primary-foreground"
                : "hover:bg-accent"
            )}
            onClick={() => setIsOpen(false)}
          >
            <Home className="h-5 w-5" />
            <span>{locale === 'zh' ? '首页' : 'Home'}</span>
          </Link>

          {/* 工具分类 */}
          <div className="space-y-4">
            {Object.entries(toolCategories).map(([categoryKey, category]) => {
              const Icon = categoryIcons[categoryKey as keyof typeof categoryIcons];
              const categoryTools = getToolsByCategory(categoryKey);
              
              return (
                <div key={categoryKey}>
                  <div className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-muted-foreground">
                    <Icon className="h-4 w-4" />
                    <span>{t(`categories.${categoryKey}`)}</span>
                  </div>
                  <div className="ml-6 space-y-1">
                    {categoryTools.map((tool) => (
                      <Link
                        key={tool.id}
                        href={`/${locale}${tool.path}`}
                        className={cn(
                          "block px-3 py-2 rounded-lg text-sm transition-colors",
                          pathname === `/${locale}${tool.path}`
                            ? "bg-primary text-primary-foreground"
                            : "hover:bg-accent"
                        )}
                        onClick={() => setIsOpen(false)}
                      >
                        {tool.name}
                      </Link>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </nav>

      {/* 移动端遮罩 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
