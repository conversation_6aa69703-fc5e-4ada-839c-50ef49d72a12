#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 工具配置映射 - 从 tools.ts 提取的已实现工具
const toolConfigs = [
  { id: 'image-compressor', toolName: 'imageCompressor', path: '/tools/image-compressor' },
  { id: 'qr-generator', toolName: 'qrGenerator', path: '/tools/qr-generator' },
  { id: 'json-formatter', toolName: 'jsonFormatter', path: '/tools/json-formatter' },
  { id: 'url-encoder', toolName: 'urlEncoder', path: '/tools/url-encoder' },
  { id: 'timestamp-converter', toolName: 'timestampConverter', path: '/tools/timestamp-converter' },
  { id: 'uuid-generator', toolName: 'uuidGenerator', path: '/tools/uuid-generator' },
  { id: 'password-generator', toolName: 'passwordGenerator', path: '/tools/password-generator' },
  { id: 'hash-calculator', toolName: 'hashCalculator', path: '/tools/hash-calculator' },
  { id: 'unit-converter', toolName: 'unitConverter', path: '/tools/unit-converter' },
  { id: 'pdf-merger', toolName: 'pdfMerger', path: '/tools/pdf-merger' },
  { id: 'pdf-splitter', toolName: 'pdfSplitter', path: '/tools/pdf-splitter' },
  { id: 'pdf-to-image', toolName: 'pdfToImage', path: '/tools/pdf-to-image' },
  { id: 'fba-label-stamper', toolName: 'fbaLabelStamper', path: '/tools/fba-label-stamper' }
];

// 生成新的 page.tsx 内容
function generatePageContent(toolConfig) {
  const { id, toolName, path: toolPath } = toolConfig;
  
  // 将 kebab-case 转换为 PascalCase
  const componentName = id.split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('') + 'Client';
  
  return `import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import { getTranslations } from 'next-intl/server';
import { getToolById } from '@/config/tools';
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import ${componentName} from './${componentName}';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: '${toolName}',
    path: '${toolPath}'
  });
}

export default async function ${componentName.replace('Client', 'Page')}({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  const t = await getTranslations('tools.${toolName}');
  const categoryT = await getTranslations('navigation.categories');
  
  // 获取工具信息
  const tool = getToolById('${id}')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);
  
  // 生成结构化数据
  const softwareSchema = generateSoftwareApplicationSchema(
    tool, 
    locale, 
    toolName, 
    toolDescription
  );
  
  const breadcrumbSchema = generateBreadcrumbSchema(
    [
      { name: '首页', url: \`/\${locale}\` },
      { name: categoryName, url: \`/\${locale}#\${tool.category}\` },
      { name: toolName, url: \`/\${locale}\${tool.path}\` }
    ],
    locale
  );

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      <${componentName} />
    </>
  );
}
`;
}

// 备份原文件
function backupFile(filePath) {
  const backupPath = filePath + '.backup';
  if (fs.existsSync(filePath)) {
    fs.copyFileSync(filePath, backupPath);
    console.log(`📁 备份文件: ${backupPath}`);
  }
}

// 处理单个工具
function processToolPage(toolConfig) {
  const { id } = toolConfig;
  const pagePath = path.join(process.cwd(), 'src', 'app', '[locale]', 'tools', id, 'page.tsx');
  
  console.log(`\n🔧 处理工具: ${id}`);
  console.log(`📄 文件路径: ${pagePath}`);
  
  // 检查文件是否存在
  if (!fs.existsSync(pagePath)) {
    console.log(`❌ 文件不存在，跳过: ${pagePath}`);
    return false;
  }
  
  // 读取当前文件内容
  const currentContent = fs.readFileSync(pagePath, 'utf8');
  
  // 检查是否已经包含结构化数据
  if (currentContent.includes('StructuredData') || currentContent.includes('generateSoftwareApplicationSchema')) {
    console.log(`✅ 已包含结构化数据，跳过: ${id}`);
    return false;
  }
  
  // 备份原文件
  backupFile(pagePath);
  
  // 生成新内容
  const newContent = generatePageContent(toolConfig);
  
  // 写入新文件
  fs.writeFileSync(pagePath, newContent, 'utf8');
  console.log(`✅ 成功更新: ${id}`);
  
  return true;
}

// 主函数
function main() {
  console.log('🚀 开始批量添加结构化数据到工具页面...\n');
  
  let processedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  
  // 排除已经处理过的 qr-generator
  const toolsToProcess = toolConfigs.filter(tool => tool.id !== 'qr-generator');
  
  for (const toolConfig of toolsToProcess) {
    try {
      const processed = processToolPage(toolConfig);
      if (processed) {
        processedCount++;
      } else {
        skippedCount++;
      }
    } catch (error) {
      console.error(`❌ 处理 ${toolConfig.id} 时出错:`, error.message);
      errorCount++;
    }
  }
  
  console.log('\n📊 处理结果统计:');
  console.log(`✅ 成功处理: ${processedCount} 个工具`);
  console.log(`⏭️  跳过: ${skippedCount} 个工具`);
  console.log(`❌ 错误: ${errorCount} 个工具`);
  
  if (processedCount > 0) {
    console.log('\n🎉 批量添加结构化数据完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. 检查修改的文件是否正确');
    console.log('2. 运行开发服务器测试: npm run dev');
    console.log('3. 验证结构化数据: node scripts/check-structured-data.js');
    console.log('4. 如有问题，可以从 .backup 文件恢复');
  }
}

// 恢复备份文件的函数
function restoreBackups() {
  console.log('🔄 开始恢复备份文件...\n');

  let restoredCount = 0;

  for (const toolConfig of toolConfigs) {
    const { id } = toolConfig;
    const pagePath = path.join(process.cwd(), 'src', 'app', '[locale]', 'tools', id, 'page.tsx');
    const backupPath = pagePath + '.backup';

    if (fs.existsSync(backupPath)) {
      fs.copyFileSync(backupPath, pagePath);
      fs.unlinkSync(backupPath); // 删除备份文件
      console.log(`✅ 恢复: ${id}`);
      restoredCount++;
    }
  }

  console.log(`\n📊 恢复了 ${restoredCount} 个文件`);
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

// 如果直接运行此脚本
if (require.main === module) {
  if (command === 'restore') {
    restoreBackups();
  } else if (command === 'help') {
    console.log('📖 使用说明:');
    console.log('  node scripts/add-structured-data-to-tools.js        # 添加结构化数据');
    console.log('  node scripts/add-structured-data-to-tools.js restore # 恢复备份文件');
    console.log('  node scripts/add-structured-data-to-tools.js help    # 显示帮助');
  } else {
    main();
  }
}

module.exports = { processToolPage, generatePageContent, toolConfigs, restoreBackups };
