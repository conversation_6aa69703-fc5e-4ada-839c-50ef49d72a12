import type { Metadata } from "next";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales, Locale } from '@/i18n/config';
import { StructuredData } from '@/components/StructuredData';
import { GoogleAnalytics } from '@/components/GoogleAnalytics';
import { generateWebsiteSchema, generateOrganizationSchema } from '@/lib/structured-data';

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'metadata' });

  // 获取当前域名，在生产环境中应该是实际域名
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    authors: [{ name: "AnyTool" }],
    robots: "index, follow",
    icons: {
      icon: [
        { url: '/favicon.ico', sizes: 'any' },
        { url: '/logo.png', type: 'image/png' }
      ],
      apple: '/logo.png'
    },
    alternates: {
      canonical: `${baseUrl}/${locale}`,
      languages: {
        'zh-CN': `${baseUrl}/zh`,
        'zh': `${baseUrl}/zh`,
        'en-US': `${baseUrl}/en`,
        'en': `${baseUrl}/en`,
        'x-default': `${baseUrl}/zh`, // 默认语言设为中文
      }
    },
    openGraph: {
      title: t('title'),
      description: t('description'),
      url: `${baseUrl}/${locale}`,
      siteName: 'AnyTool',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    }
  };
}

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  // 获取结构化数据翻译
  const structuredDataT = await getTranslations({ locale, namespace: 'structuredData' });

  // 生成结构化数据
  const websiteSchema = await generateWebsiteSchema(locale, structuredDataT);
  const organizationSchema = await generateOrganizationSchema(locale, structuredDataT);

  // 获取 Google Analytics ID
  const gaId = process.env.NEXT_PUBLIC_GA_ID;

  return (
    <html lang={locale}>
      <body>
        <StructuredData data={[websiteSchema, organizationSchema]} />
        {gaId && <GoogleAnalytics gaId={gaId} />}
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
