'use client';

import { useState, useRef, useCallback } from 'react';
import { Upload, FileText, X, CheckCircle, AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface DragDropUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  accept?: string;
  maxSize?: number; // in MB
  currentFile?: File | null;
  isProcessing?: boolean;
  uploadStatus?: 'idle' | 'uploading' | 'success' | 'error';
  errorMessage?: string;
  className?: string;
}

export function DragDropUpload({
  onFileSelect,
  onFileRemove,
  accept = '.pdf',
  maxSize = 50,
  currentFile,
  isProcessing = false,
  uploadStatus = 'idle',
  errorMessage,
  className
}: DragDropUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const t = useTranslations('components.dragDropUpload');

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const file = files[0];

    if (file) {
      validateAndSelectFile(file);
    }
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      validateAndSelectFile(file);
    }
  }, []);

  const validateAndSelectFile = (file: File) => {
    // 验证文件类型
    if (!file.type.includes('pdf')) {
      return;
    }

    // 验证文件大小
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      return;
    }

    onFileSelect(file);
  };

  const handleRemoveFile = () => {
    onFileRemove();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getStatusIcon = () => {
    switch (uploadStatus) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-success-500" />;
      case 'error':
        return <AlertCircle className="h-6 w-6 text-error-500" />;
      default:
        return <Upload className="h-8 w-8 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    if (uploadStatus === 'error') return 'border-error-500 bg-error-50';
    if (uploadStatus === 'success') return 'border-success-500 bg-success-50';
    if (isDragOver) return 'border-primary-500 bg-primary-50';
    return 'border-gray-300 bg-gray-50';
  };

  return (
    <div className={cn('w-full', className)}>
      {!currentFile ? (
        // 上传区域
        <div
          className={cn(
            'relative border-2 border-dashed rounded-lg transition-all duration-200',
            'hover:border-primary-400 hover:bg-primary-25',
            'cursor-pointer group',
            getStatusColor(),
            isProcessing && 'pointer-events-none opacity-50'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="p-6 md:p-8 text-center space-y-4">
            {/* 图标 */}
            <div className="flex justify-center">
              {getStatusIcon()}
            </div>

            {/* 主要文本 */}
            <div className="space-y-2">
              <p className="text-lg font-medium text-gray-900">
                {t('clickOrDrag')}
              </p>
              <p className="text-sm text-gray-500">
                {t('supportedFormats', { maxSize })}
              </p>
            </div>

            {/* 选择按钮 */}
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="pointer-events-none"
            >
              <Upload className="h-4 w-4 mr-2" />
              {t('selectFile')}
            </Button>

            {/* 错误信息 */}
            {uploadStatus === 'error' && errorMessage && (
              <div className="text-sm text-error-600 bg-error-50 p-3 rounded-md">
                {errorMessage}
              </div>
            )}
          </div>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            onChange={handleFileInputChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isProcessing}
          />
        </div>
      ) : (
        // 文件信息卡片
        <div className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <FileText className="h-8 w-8 text-primary-500 flex-shrink-0 mt-1" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {currentFile.name}
                </p>
                <div className="mt-1 space-y-1">
                  <p className="text-xs text-gray-500">
                    {t('size')}: {(currentFile.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                  <div className="flex items-center space-x-2">
                    {uploadStatus === 'success' && (
                      <>
                        <CheckCircle className="h-4 w-4 text-success-500" />
                        <span className="text-xs text-success-600">{t('uploaded')}</span>
                      </>
                    )}
                    {uploadStatus === 'error' && (
                      <>
                        <AlertCircle className="h-4 w-4 text-error-500" />
                        <span className="text-xs text-error-600">{t('uploadFailed')}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            {!isProcessing && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemoveFile}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {uploadStatus === 'error' && errorMessage && (
            <div className="mt-3 text-xs text-error-600 bg-error-50 p-2 rounded">
              {errorMessage}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
