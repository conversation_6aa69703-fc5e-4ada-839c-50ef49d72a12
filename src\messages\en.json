{"metadata": {"title": "AnyTool - Free Online Tools Collection", "description": "Provides various practical online tools including image processing, text processing, data conversion, PDF operations, completely free to use", "keywords": "online tools,image compression,text processing,data conversion,PDF tools,free tools"}, "navigation": {"title": "AnyTool", "home": "Home", "search": "Search tools...", "categories": {"image": "Image", "text": "Text", "data": "Data", "utility": "Utility", "pdf": "PDF"}, "languageSwitch": "Switch Language", "tools": {"image-compressor": "Image Compressor", "image-converter": "Image Converter", "image-cropper": "Image Cropper", "image-resizer": "Image Resizer", "image-watermark": "Image Watermark", "image-joiner": "Image Joiner", "text-diff": "Text Diff", "markdown-converter": "Markdown Converter", "text-analyzer": "Text Analyzer", "text-formatter": "Text Formatter", "regex-tester": "Regex Tester", "text-crypto": "Text Crypto", "qr-generator": "QR Generator", "json-formatter": "JSON Formatter", "csv-converter": "CSV Converter", "url-encoder": "URL Encoder", "timestamp-converter": "Timestamp Converter", "color-converter": "Color Converter", "uuid-generator": "UUID Generator", "password-generator": "Password Generator", "hash-calculator": "Hash Calculator", "unit-converter": "Unit Converter", "base-converter": "Base Converter", "random-generator": "Random Generator", "pdf-merger": "PDF Merger", "pdf-splitter": "PDF Splitter", "pdf-to-image": "PDF to Image", "fba-label-stamper": "FBA Label Stamper"}}, "components": {"dragDropUpload": {"clickOrDrag": "Click to select or drag PDF files", "supportedFormats": "Supported formats: PDF (max {maxSize}MB)", "selectFile": "Select File", "size": "Size", "uploaded": "Uploaded", "uploadFailed": "Upload Failed"}, "fbaSettingsPanel": {"textSettings": "Text Settings", "advancedOptions": "& Advanced Options", "addText": "Add Text", "textDescription": "This text will be added near detected \"新品\" or \"New\" locations", "advancedOptionsLabel": "Advanced Options", "fontSize": "Font Size", "fontSizeRange": "px (6-20)", "positionAdjustment": "Position Adjustment", "horizontalOffset": "Horizontal Offset", "verticalOffset": "Vertical Offset", "positionDescription": "Fine-tune text offset relative to the detected position", "debugMode": "Enable Debug Mode", "debugModeDescription": "Show text detection areas to help debug positioning issues"}, "fbaActionButtons": {"processing": "Processing...", "processFile": "Process File", "downloaded": "Downloaded", "downloadResult": "Download Result", "reset": "Reset", "processHint": "💡 Click \"Process File\" to start adding \"Made in China\" labels to your PDF", "processCompleted": "✅ Processing complete!"}}, "home": {"hero": {"title": "Powerful Online Tools Collection", "subtitle": "Provides image processing, text conversion, data operations and more practical tools", "description": "No download or installation required, use directly in your browser. All tools are completely free and protect your privacy.", "cta": "Get Started"}, "features": {"title": "Why Choose AnyTool?", "fast": {"title": "Fast & Efficient", "description": "All tools run in your browser, no waiting for uploads or downloads"}, "free": {"title": "Completely Free", "description": "All features are permanently free to use, no hidden fees"}, "easy": {"title": "Easy to Use", "description": "Clean and intuitive interface design, ready to use without learning"}}, "categories": {"title": "Tool Categories", "viewAll": "View All"}, "popular": {"title": "Popular Tools"}}, "tools": {"imageCompressor": {"name": "Image Compressor", "description": "Compress JPG, PNG images to reduce file size", "keywords": "image compression, photo compressor, reduce file size, JPG compression, PNG compression, WebP converter, image optimizer, photo resizer, image quality, file size reducer, batch compression, online image tool, photo optimization, web optimization", "title": "Image Compressor", "subtitle": "Compress image file size while maintaining good visual quality", "upload": {"title": "Drag images here or click to select", "subtitle": "Supports JPG, PNG, WebP formats, multiple files allowed", "button": "Select Images"}, "settings": {"title": "Compression Settings", "description": "Adjust these parameters to control compression effect", "quality": "Image Quality", "maxWidth": "<PERSON> (px)", "maxHeight": "Max Height (px)", "format": "Output Format", "compress": "Start Compression"}, "processing": {"title": "Compressing images..."}, "result": {"title": "Compression Result", "images": "images", "downloadAll": "Download All", "clear": "Clear", "original": "Original File", "compressed": "Compressed", "reduction": "Reduced by", "download": "Download"}, "info": {"title": "Instructions", "items": ["Supports JPG, PNG, WebP format image compression", "Adjust compression quality to balance file size and image quality", "Support setting maximum width and height with automatic proportional scaling", "All processing is done in the browser, no upload to server", "Download directly after compression, protecting your privacy"]}}, "imageConverter": {"name": "Image Converter", "description": "Convert between JPG/PNG/WebP/AVIF formats", "keywords": "image format conversion, image converter, JPG to PNG, PNG to JPG, WebP converter, AVIF converter, image format changer, image type converter, batch conversion, online converter, image format tool, image processing", "title": "Image Format Converter", "subtitle": "Convert between JPG, PNG, WebP, AVIF formats while maintaining image quality", "settings": {"title": "Conversion Settings", "description": "Choose target format and quality settings", "format": "Target Format", "quality": "Image Quality"}, "upload": {"title": "Select Image Files", "description": "Supports multiple formats, select multiple files at once", "dragText": "Drag images here or click to select", "selectFiles": "Select Image Files", "supportedFormats": "Supports JPG, PNG, WebP, BMP, GIF formats", "processing": "Converting..."}, "results": {"title": "Conversion Results", "downloadAll": "Download All", "clearAll": "Clear All", "download": "Download"}, "info": {"title": "Instructions", "items": ["Supports JPG, PNG, WebP, BMP, GIF format conversion", "Adjustable JPEG quality to balance file size and image quality", "Supports batch conversion for improved efficiency", "All processing is done in your browser, no server upload", "Download converted images directly, protecting your privacy"]}}, "jsonFormatter": {"name": "JSON Formatter", "description": "Format, minify and validate JSON data", "keywords": "JSON formatter, JSO<PERSON> beautifier, JSON minifier, JSON validator, JSON parser, JSON pretty print, JSON syntax checker, JSON editor, API response formatter, JSON lint, data formatting, JSON viewer, JSON converter", "title": "JSON Formatter", "subtitle": "Beautify, minify, validate JSON data with syntax highlighting and error detection", "actions": {"title": "Action Panel", "description": "Select operation type and settings", "format": "Format", "minify": "Minify", "validate": "Validate", "loadSample": "<PERSON><PERSON>", "clear": "Clear", "indentSize": "Indent Size", "spaces": "spaces", "upload": "Upload File", "copy": "Copy", "copied": "<PERSON>pied", "download": "Download"}, "input": {"title": "Input JSON", "description": "Paste or enter JSON data to process", "placeholder": "Enter JSON data here..."}, "result": {"title": "Output Result", "description": "Formatted JSON data", "placeholder": "Formatted result will be displayed here...", "error": "JSON Error", "valid": "JSON format is valid", "invalid": "JSON format is invalid"}, "info": {"title": "Instructions", "items": ["<strong>Format:</strong> Convert minified JSON to readable format", "<strong>Minify:</strong> Remove all unnecessary spaces and line breaks", "<strong>Validate:</strong> Check if JSON syntax is correct", "Support uploading .json and .txt files", "Adjust indent size to control formatting style", "All processing is done in the browser, protecting data privacy"]}}, "qrGenerator": {"name": "QR Code Generator", "description": "Generate various types of QR codes", "keywords": "QR code generator, QR code maker, barcode generator, WiFi QR code, URL QR code, contact QR code, SMS QR code, email QR code, QR scanner, mobile QR code, quick response code, 2D barcode, QR code creator", "title": "QR Code Generator", "subtitle": "Generate QR codes for URLs, text, WiFi and other types", "content": {"title": "Input Content", "description": "Enter text or link to generate QR code", "placeholder": "Enter text, links, WiFi info, etc...", "quickTemplates": "Quick Templates", "type": "Content Type", "types": {"text": "Plain Text", "url": "Website Link", "email": "Email", "phone": "Phone Number", "sms": "SMS", "wifi": "WiFi Connection"}, "placeholders": {"text": "Enter text to generate QR code...", "url": "https://example.com", "email": "<EMAIL>", "phone": "****** 123 4567", "sms": "Enter SMS content...", "wifi": {"ssid": "WiFi Network Name", "password": "WiFi Password", "security": "Security Type"}}}, "settings": {"title": "Custom Settings", "description": "Adjust QR code appearance and properties", "size": "Size", "errorCorrection": "Error Correction Level", "errorLevels": {"low": "Low (~7%)", "lowDesc": "Suitable for clean environments", "medium": "Medium (~15%)", "mediumDesc": "Recommended", "quartile": "Quartile (~25%)", "quartileDesc": "Suitable for general environments", "high": "High (~30%)", "highDesc": "Suitable for harsh environments"}, "foregroundColor": "Foreground Color", "backgroundColor": "Background Color", "generate": "Generate QR Code"}, "result": {"title": "QR Code Preview", "description": "Generated QR code preview", "downloadPng": "PNG", "downloadSvg": "SVG", "pixels": "pixels", "placeholder": "Enter content to generate QR code"}, "info": {"title": "Instructions", "items": ["Supports text, links, WiFi, email, phone and other formats", "Customize QR code size, color and error correction level", "Support PNG and SVG format downloads", "Higher error correction level makes QR codes more complex but more fault-tolerant", "WiFi format: WIFI:T:WPA;S:NetworkName;P:Password;;", "All processing is done in the browser, protecting privacy and security"]}}, "pdfMerger": {"name": "PDF Merger", "description": "Merge multiple PDF files into one", "keywords": "PDF merger, combine PDF, merge PDF files, PDF joiner, PDF combiner, join PDF, concatenate PDF, PDF tools, document merger, file merger, PDF editor, batch PDF processing", "title": "PDF Merger", "subtitle": "Merge multiple PDF files into one file with drag-and-drop sorting and preview", "upload": {"title": "Drag PDF files here or click to select", "description": "Supports multiple PDF files, will be merged in list order", "button": "Select PDF Files"}, "fileList": {"title": "PDF File List ({count} files)", "description": "Total {totalPages} pages • Drag to adjust merge order", "mergeButton": "Merge PDFs", "clearButton": "Clear", "merging": "Merging...", "pages": "{count} pages", "position": "File {position}"}, "processing": {"message": "Merging PDF files..."}, "result": {"title": "<PERSON><PERSON>", "description": "PDF files have been successfully merged, click to download", "downloadButton": "Download Merged PDF"}, "info": {"title": "Instructions", "items": ["Support selecting multiple PDF files for merging", "Use up/down arrows to adjust file merge order", "Merged file will contain all pages from original files", "All processing is done in the browser, no upload to server", "Support drag-and-drop upload for easier operation"]}}, "pdfSplitter": {"name": "PDF Splitter", "description": "Split PDF into multiple files by page count", "keywords": "PDF splitter, split PDF, divide PDF, PDF separator, extract PDF pages, PDF page extractor, break PDF, PDF tools, document splitter, page range extractor, PDF editor", "title": "PDF Splitter", "subtitle": "Split PDF files by page count or custom ranges into multiple files", "upload": {"title": "Drag PDF file here or click to select", "subtitle": "Select the PDF file to split", "button": "Select PDF File"}, "fileInfo": {"title": "File Information", "filename": "File Name", "filesize": "File Size", "totalPages": "Total Pages"}, "settings": {"title": "Split Settings", "description": "Choose split method and parameters", "mode": "Split Method", "modes": {"pages": "Split by <PERSON> Count", "range": "Custom Range"}, "pagesPerFile": "Pages per File", "willGenerate": "Will generate {count} files", "pageRange": "Page Range (e.g., 1-3, 5, 7-10)", "pageRangePlaceholder": "1-3, 5, 7-10", "pageRangeDescription": "Separate multiple ranges with commas, supports single pages and page ranges", "split": "Start Split", "splitting": "Splitting..."}, "processing": {"title": "Splitting PDF file..."}, "result": {"title": "Split Result", "description": "Click to download individual files or batch download", "downloadAll": "Download All", "clear": "Clear", "download": "Download"}, "info": {"title": "Instructions", "items": ["Split by page count: Split PDF evenly by specified page count", "Custom range: Specify exact page ranges for splitting", "Range format: 1-3 means pages 1 to 3, 5 means page 5, separate with commas", "All processing is done in the browser, no upload to server", "Support batch download of all split files"]}}, "pdfToImage": {"name": "PDF to Image", "description": "Convert PDF pages to JPG/PNG images", "keywords": "PDF to image, PDF to JPG, PDF to PNG, convert PDF, PDF converter, extract images from PDF, PDF page to image, document converter, PDF tools, image converter, PDF extractor", "title": "PDF to Image", "subtitle": "Convert PDF pages to JPG or PNG images with custom quality and size", "upload": {"title": "Drag PDF file here or click to select", "subtitle": "Select the PDF file to convert", "button": "Select PDF File", "loading": "Loading PDF processing library..."}, "fileInfo": {"title": "File Information", "filename": "File Name", "filesize": "File Size", "totalPages": "Total Pages"}, "settings": {"title": "Conversion Settings", "description": "Configure output image format and quality", "format": "Image Format", "formats": {"png": "PNG (Lossless)", "jpeg": "JPEG (Lossy)"}, "scale": "Scale ({scale}x)", "scaleDescription": "Higher scale produces clearer but larger images", "quality": "JPEG Quality ({quality}%)", "pageSelection": "Page Selection", "pageSelectionPlaceholder": "all or 1-3, 5, 7-10", "pageSelectionDescription": "Enter \"all\" to convert all pages, or specify page ranges (e.g., 1-3, 5, 7-10)", "convert": "Start Conversion", "converting": "Converting..."}, "processing": {"title": "Converting PDF pages to images..."}, "result": {"title": "Conversion Result", "description": "Click to preview or download images", "downloadAll": "Download All", "clear": "Clear", "download": "Download", "page": "Page {page}"}, "info": {"title": "Instructions", "items": ["PNG format: Lossless compression, suitable for documents and charts", "JPEG format: Lossy compression, smaller files, suitable for photos", "Scale: Controls output image resolution and clarity", "Page selection supports range format: 1-3 means pages 1 to 3, separate with commas", "All processing is done in the browser, protecting file privacy"]}}, "fbaLabelStamper": {"name": "FBA Label Stamper", "description": "Free Amazon FBA Label Online One-Click Add Made in China", "keywords": "FBA label, Amazon FBA, Made in China, PDF stamper, label modifier, Amazon seller tools, FBA compliance, product labeling, PDF editor, Amazon logistics, FBA requirements, label automation, Chinese products, origin labeling, Amazon marketplace", "title": "FBA Label Stamper", "subtitle": "Automatically add \"Made in China\" text to Amazon FBA labels, intelligently locate \"新品\" or \"New\" position", "upload": {"title": "Upload PDF File", "description": "Select FBA label PDF file to add text", "button": "Select PDF File", "selected": "Selected: {filename} ({size} MB)"}, "textSettings": {"title": "Text Settings", "description": "Configure the text content to add", "textToAdd": "Text to Add", "placeholder": "Made in China"}, "advancedSettings": {"title": "Advanced Settings", "description": "Adjust text position and style", "fontSize": "Font Size (pt)", "xOffset": "Horizontal Offset (pt)", "yOffset": "Vertical Offset (pt)", "debugMode": "Debug Mode"}, "preview": {"title": "PDF Preview", "description": "Preview of your PDF file", "original": "Original PDF", "processed": "Processed PDF (with stamp)", "noPreview": "No PDF loaded for preview", "loading": "Loading preview...", "error": "Failed to load preview"}, "process": {"title": "Start Processing", "description": "Click button to start processing PDF file", "button": "Process PDF", "processing": "Processing...", "reset": "Reset"}, "download": {"title": "Download Processed PDF", "description": "Download the PDF with added stamp", "button": "Download Processed PDF", "notReady": "Process the PDF first to enable download"}, "status": {"selectFile": "Please select a PDF file first", "libraryLoading": "Libraries not loaded yet, please wait", "libraryLoaded": "Libraries loaded, ready to process files", "libraryFailed": "Failed to load libraries, please refresh and try again", "analyzing": "Analyzing PDF text...", "processing": "Adding text...", "generating": "Generating processed PDF...", "completed": "Processing completed! Processed {count} labels, preview updated with processed PDF.", "failed": "Processing failed: {error}", "previewUpdated": "Preview updated with processed PDF"}, "info": {"title": "Instructions", "items": ["Upload FBA label PDF file containing \"新品\" or \"New\" text", "Tool will automatically locate \"新品\" text position, if not found, it will try to locate \"New\"", "Add specified text after the target text (default: \"Made in China\")", "Adjust text size, position offset and other parameters", "Enable debug mode to view recognized text areas", "Preview and download modified PDF file after processing"]}}, "passwordGenerator": {"name": "Password Generator", "description": "Generate secure passwords with custom rules", "keywords": "password generator, secure password, random password, strong password, password maker, password creator, cybersecurity, password strength, secure login, password policy, random string generator, authentication, password security", "title": "Password Generator", "subtitle": "Generate strong and secure passwords with customizable rules and options", "settings": {"title": "Password Settings", "description": "Configure password generation rules and options", "length": "Password Length", "lengthDescription": "Recommended: 12-16 characters for strong security", "includeUppercase": "Include Uppercase Letters (A-Z)", "includeLowercase": "Include Lowercase Letters (a-z)", "includeNumbers": "Include Numbers (0-9)", "includeSymbols": "Include Symbols (!@#$%^&*)", "excludeSimilar": "Exclude Similar Characters (0, O, l, I)", "excludeAmbiguous": "Exclude Ambiguous Characters ({}, [], (), /\\, ~, `, etc.)", "customCharacters": "Custom Characters", "customCharactersPlaceholder": "Enter custom characters to include...", "generate": "Generate Password", "generateMultiple": "Generate Multiple ({count})"}, "result": {"title": "Generated Password", "multipleTitle": "Generated Passwords ({count})", "strength": "Password Strength", "strengthLevels": {"weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong", "veryStrong": "Very Strong"}, "copy": "Copy", "copied": "Copied!", "copyAll": "Copy All", "regenerate": "Regenerate", "clear": "Clear"}, "analysis": {"title": "Password Analysis", "length": "Length: {length} characters", "entropy": "Entropy: {entropy} bits", "timeToCrack": "Time to crack: {time}", "timeToCrackValues": {"instant": "Instant", "seconds": "{count} seconds", "minutes": "{count} minutes", "hours": "{count} hours", "days": "{count} days", "months": "{count} months", "years": "{count} years", "centuries": "{count} centuries"}}, "info": {"title": "Security Tips", "items": ["<strong>Length matters:</strong> Longer passwords are exponentially harder to crack", "<strong>Use variety:</strong> Include uppercase, lowercase, numbers, and symbols", "<strong>Avoid patterns:</strong> Don't use dictionary words, personal info, or predictable patterns", "<strong>Unique passwords:</strong> Use different passwords for different accounts", "<strong>Password manager:</strong> Consider using a password manager to store complex passwords", "<strong>Regular updates:</strong> Change passwords regularly, especially for important accounts"]}}, "urlEncoder": {"name": "URL Encoder/Decoder", "description": "Encode and decode URLs and URL components", "keywords": "URL encoder, URL decoder, percent encoding, URL escape, URI encoding, query parameter encoder, Base64 encoder, web development, API parameters, URL safe encoding, character encoding, web tools", "title": "URL Encoder/Decoder", "subtitle": "Encode and decode URLs, query parameters, and URL components with support for different encoding types", "input": {"title": "Input Text", "description": "Enter the text or URL to encode/decode", "placeholder": "Enter URL or text to encode/decode...", "loadSample": "<PERSON><PERSON>", "clear": "Clear"}, "actions": {"title": "Actions", "description": "Choose encoding/decoding operation", "encode": "Encode", "decode": "Decode", "encodeComponent": "Encode Component", "decodeComponent": "Decode Component", "encodeBase64": "Base64 Encode", "decodeBase64": "Base64 Decode"}, "result": {"title": "Result", "description": "Encoded/decoded result", "placeholder": "Result will appear here...", "copy": "Copy", "copied": "Copied!", "download": "Download"}, "examples": {"title": "Common Examples", "url": {"title": "Full URL", "original": "https://example.com/search?q=hello world&lang=en", "encoded": "https%3A//example.com/search%3Fq%3Dhello%20world%26lang%3Den"}, "component": {"title": "URL Component", "original": "hello world & special chars!", "encoded": "hello%20world%20%26%20special%20chars%21"}, "query": {"title": "Query Parameter", "original": "user name with spaces", "encoded": "user%20name%20with%20spaces"}}, "info": {"title": "Usage Guide", "items": ["<strong>URL Encode:</strong> Converts special characters to percent-encoded format for safe URL transmission", "<strong>URL Decode:</strong> Converts percent-encoded characters back to original format", "<strong>Component Encode:</strong> Encodes only URL component parts (query parameters, path segments)", "<strong>Base64:</strong> Alternative encoding method for binary data or special characters", "<strong>Common use cases:</strong> API parameters, form data, search queries, file names in URLs", "All processing is done in the browser for privacy protection"]}}, "uuidGenerator": {"name": "UUID Generator", "description": "Generate various versions of UUIDs", "keywords": "UUID generator, GUID generator, unique identifier, UUID v1, UUID v4, UUID v7, universally unique identifier, random ID generator, database ID, API key generator, unique string, identifier generator", "title": "UUID Generator", "subtitle": "Generate various versions of Universally Unique Identifiers (UUIDs) with batch generation and custom formats", "generator": {"title": "UUID Generator", "description": "Select UUID version and generation count", "version": "UUID Version", "versions": {"v1": "Version 1 (timestamp and MAC address based)", "v4": "Version 4 (random, recommended)", "v7": "Version 7 (timestamp-based, sortable)"}, "count": "Generation Count", "countDescription": "Generate multiple UUIDs at once", "format": "Output Format", "formats": {"standard": "Standard format (with hyphens)", "compact": "Compact format (no hyphens)", "uppercase": "Uppercase format", "braces": "Braces format {UUID}", "quotes": "Quotes format \"UUID\""}, "generate": "Generate UUID", "generateMultiple": "Generate {count} UUIDs"}, "result": {"title": "Generated UUID", "multipleTitle": "Generated UUIDs ({count} items)", "copy": "Copy", "copied": "Copied!", "copyAll": "Copy All", "download": "Download", "clear": "Clear", "regenerate": "Regenerate"}, "examples": {"title": "UUID Examples", "v1": {"title": "UUID v1 (Time-based)", "description": "Contains timestamp and node information, traceable generation time", "example": "6ba7b810-9dad-11d1-80b4-00c04fd430c8"}, "v4": {"title": "UUID v4 (Random)", "description": "Completely random generation, most commonly used version", "example": "550e8400-e29b-41d4-a716-************"}, "v7": {"title": "UUID v7 (Time-ordered)", "description": "Timestamp-based, supports time-based sorting", "example": "01890a5d-ac96-774b-bcce-b302099a8057"}}, "validator": {"title": "UUID Validator", "description": "Validate UUID format correctness", "input": "Input UUID", "placeholder": "Enter UUID to validate...", "validate": "Validate", "valid": "Valid <PERSON>", "invalid": "Invalid UUID", "details": {"version": "Version: {version}", "variant": "Variant: {variant}", "timestamp": "Timestamp: {timestamp}", "node": "Node: {node}"}}, "info": {"title": "UUID Information", "items": ["<strong>UUID v1:</strong> Based on timestamp and MAC address, traceable generation time and location", "<strong>UUID v4:</strong> Completely random generation, most commonly used, high security", "<strong>UUID v7:</strong> Timestamp-based, supports time-based sorting, suitable for database primary keys", "<strong>Uniqueness:</strong> UUIDs are globally unique with extremely low probability of duplication", "<strong>Format:</strong> Standard format is 8-4-4-4-12 hexadecimal characters", "All generation is done in the browser, protecting privacy and security"]}}, "timestampConverter": {"name": "Timestamp Converter", "description": "Convert between timestamps and human-readable dates", "keywords": "timestamp converter, Unix timestamp, epoch converter, date converter, time converter, timestamp to date, date to timestamp, Unix time, milliseconds converter, ISO 8601, RFC 2822, timezone converter", "title": "Timestamp Converter", "subtitle": "Convert between Unix timestamps and human-readable dates with timezone support", "current": {"title": "Current Time", "description": "Current timestamp and date", "timestamp": "Current Timestamp", "date": "Current Date", "timezone": "Timezone"}, "converter": {"title": "Timestamp Converter", "description": "Convert between timestamp and date formats", "timestampToDate": "Timestamp to Date", "dateToTimestamp": "Date to Timestamp", "timestampInput": "Enter timestamp", "timestampPlaceholder": "1640995200", "dateInput": "Select date and time", "convert": "Convert", "result": "Result", "copy": "Copy", "copied": "Copied!", "clear": "Clear"}, "formats": {"title": "Common Formats", "description": "Timestamp in different formats", "seconds": "Seconds (Unix)", "milliseconds": "Milliseconds (JavaScript)", "iso": "ISO 8601", "rfc": "RFC 2822", "relative": "Relative Time"}, "batch": {"title": "Batch Conversion", "description": "Convert multiple timestamps at once", "input": "Enter timestamps (one per line)", "placeholder": "1640995200\n1641081600\n1641168000", "convertAll": "Convert All", "results": "Conversion Results", "download": "Download CSV"}, "examples": {"title": "Common Examples", "now": {"title": "Current Time", "timestamp": "Current timestamp", "date": "Current date"}, "epoch": {"title": "Unix Epoch", "timestamp": "0", "date": "January 1, 1970 00:00:00 UTC"}, "y2k": {"title": "Year 2000", "timestamp": "946684800", "date": "January 1, 2000 00:00:00 UTC"}}, "info": {"title": "Usage Guide", "items": ["<strong>Unix Timestamp:</strong> Seconds since January 1, 1970 00:00:00 UTC", "<strong>JavaScript Timestamp:</strong> Milliseconds since Unix epoch", "<strong>Timezone Support:</strong> Convert to different timezones", "<strong>Batch Processing:</strong> Convert multiple timestamps at once", "<strong>Common formats:</strong> ISO 8601, RFC 2822, relative time", "All processing is done in the browser for privacy protection"]}}, "unitConverter": {"name": "Unit Converter", "description": "Convert between different units of measurement", "keywords": "unit converter, measurement converter, length converter, weight converter, temperature converter, area converter, volume converter, metric converter, imperial converter, cooking converter, distance converter, speed converter", "title": "Unit Converter", "subtitle": "Convert between different units of measurement including length, weight, temperature, area, volume and more", "categories": {"length": "Length", "weight": "Weight", "temperature": "Temperature", "area": "Area", "volume": "Volume", "time": "Time", "speed": "Speed", "energy": "Energy"}, "units": {"length": {"mm": "Millimeter (mm)", "cm": "Centimeter (cm)", "m": "Meter (m)", "km": "Kilometer (km)", "in": "Inch (in)", "ft": "Foot (ft)", "yd": "Yard (yd)", "mi": "Mile (mi)"}, "weight": {"mg": "Milligram (mg)", "g": "Gram (g)", "kg": "Kilogram (kg)", "t": "Ton (t)", "oz": "Ounce (oz)", "lb": "Pound (lb)", "st": "Stone (st)"}, "temperature": {"c": "Celsius (°C)", "f": "Fahrenheit (°F)", "k": "<PERSON><PERSON> (K)", "r": "Rankine (°R)"}, "area": {"mm2": "Square Millimeter (mm²)", "cm2": "Square Centimeter (cm²)", "m2": "Square Meter (m²)", "km2": "Square Kilometer (km²)", "in2": "Square Inch (in²)", "ft2": "Square Foot (ft²)", "yd2": "Square Yard (yd²)", "mi2": "Square Mile (mi²)", "acre": "Acre", "ha": "Hectare (ha)"}, "volume": {"ml": "Milliliter (ml)", "l": "Liter (l)", "m3": "Cubic Meter (m³)", "in3": "Cubic Inch (in³)", "ft3": "Cubic Foot (ft³)", "gal": "Gallon (gal)", "qt": "Quart (qt)", "pt": "Pint (pt)", "cup": "Cup", "fl_oz": "Fluid Ounce (fl oz)"}, "time": {"ms": "Millisecond (ms)", "s": "Second (s)", "min": "Minute (min)", "h": "Hour (h)", "d": "Day (d)", "w": "Week (w)", "mo": "Month (mo)", "y": "Year (y)"}, "speed": {"mps": "Meter per Second (m/s)", "kph": "Kilometer per Hour (km/h)", "mph": "Mile per Hour (mph)", "fps": "Foot per Second (ft/s)", "knot": "Knot"}, "energy": {"j": "<PERSON><PERSON> (J)", "kj": "Kilojoule (kJ)", "cal": "Calorie (cal)", "kcal": "Kilocalorie (kcal)", "wh": "Watt-hour (Wh)", "kwh": "Kilowatt-hour (kWh)", "btu": "British Thermal Unit (BTU)"}}, "converter": {"title": "Unit Conversion", "description": "Select category and units to convert", "category": "Conversion Category", "fromUnit": "From", "toUnit": "To", "fromValue": "Input Value", "toValue": "Conversion Result", "fromPlaceholder": "Enter value to convert...", "swap": "Swap Units", "clear": "Clear", "copy": "<PERSON><PERSON> Result", "copied": "Copied!"}, "quickConvert": {"title": "Quick Convert", "description": "Common unit conversions", "common": [{"name": "Centimeters to Inches", "category": "length", "from": "cm", "to": "in", "value": "30"}, {"name": "Kilograms to Pounds", "category": "weight", "from": "kg", "to": "lb", "value": "70"}, {"name": "Celsius to Fahrenheit", "category": "temperature", "from": "c", "to": "f", "value": "25"}, {"name": "Liters to Gallons", "category": "volume", "from": "l", "to": "gal", "value": "10"}]}, "examples": {"title": "Common Uses", "cooking": {"title": "Cooking Measurements", "description": "Convert weight and volume units in recipes"}, "travel": {"title": "Travel Planning", "description": "Convert distance, speed and temperature units"}, "construction": {"title": "Construction", "description": "Convert length, area and volume units"}, "science": {"title": "Scientific Calculations", "description": "Convert various physical quantity units"}}, "info": {"title": "Usage Guide", "items": ["Supports 8 categories of unit conversion: length, weight, temperature, area, volume, time, speed, energy", "Provides quick conversion templates for common units", "Supports high-precision calculations with appropriate decimal places", "Temperature conversion supports Celsius, Fahrenheit, Kelvin and Rankine", "All calculations are performed in the browser, protecting privacy", "Supports copying conversion results and swapping units"]}}, "hashCalculator": {"name": "Hash Calculator", "description": "Calculate hash values for files or text", "keywords": "hash calculator, MD5 generator, SHA1 generator, SHA256 generator, SHA512 generator, checksum calculator, file integrity, hash verification, cryptographic hash, file checksum, data integrity, security hash", "title": "Hash Calculator", "subtitle": "Calculate MD5, SHA1, SHA256 and other hash values for files or text, used for file verification and security validation", "input": {"title": "Input Data", "description": "Select input type and provide data", "type": "Input Type", "types": {"text": "Text Input", "file": "File Upload"}, "textPlaceholder": "Enter text to calculate hash...", "fileUpload": "Drag files here or click to select", "fileButton": "Select File", "loadSample": "<PERSON><PERSON>", "clear": "Clear"}, "algorithms": {"title": "Hash Algorithms", "description": "Select hash algorithms to use", "md5": "MD5 (128-bit)", "sha1": "SHA-1 (160-bit)", "sha256": "SHA-256 (256-bit)", "sha512": "SHA-512 (512-bit)", "selectAll": "Select All", "deselectAll": "Deselect All"}, "calculate": "Calculate Hash", "calculating": "Calculating...", "result": {"title": "Hash Results", "description": "Calculated hash values", "algorithm": "Algorithm", "hash": "Hash Value", "copy": "Copy", "copied": "Copied!", "copyAll": "Copy All", "download": "Download Results", "clear": "Clear Results"}, "fileInfo": {"title": "File Information", "name": "File Name", "size": "File Size", "type": "File Type", "lastModified": "Last Modified"}, "verification": {"title": "Hash Verification", "description": "Enter known hash value for verification", "input": "Enter <PERSON>", "placeholder": "Enter hash value to verify...", "verify": "Verify", "match": "✓ Hash values match", "noMatch": "✗ Hash values do not match", "selectHash": "Select hash algorithm to verify"}, "examples": {"title": "Common Uses", "fileIntegrity": {"title": "File Integrity Verification", "description": "Verify downloaded files are complete and unmodified"}, "passwordSecurity": {"title": "Password Security Storage", "description": "Convert passwords to hash values for secure storage"}, "dataVerification": {"title": "Data Consistency Check", "description": "Ensure data hasn't been modified during transmission"}}, "info": {"title": "Algorithm Information", "items": ["<strong>MD5:</strong> Fast but less secure, suitable for non-security checksums", "<strong>SHA-1:</strong> More secure than MD5, but no longer recommended for security applications", "<strong>SHA-256:</strong> Currently widely used secure hash algorithm, recommended", "<strong>SHA-512:</strong> Higher security hash algorithm, suitable for high-security requirements", "<strong>Uses:</strong> File verification, password storage, digital signatures, blockchain, etc.", "All calculations are performed in the browser, protecting file and data privacy"]}}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "download": "Download", "upload": "Upload", "clear": "Clear", "copy": "Copy", "paste": "Paste", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "retry": "Retry", "fileSize": "File Size", "fileName": "File Name", "format": "Format", "quality": "Quality", "size": "Size", "color": "Color", "settings": "Settings", "preview": "Preview", "result": "Result", "processing": "Processing", "completed": "Completed", "failed": "Failed", "bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB"}}