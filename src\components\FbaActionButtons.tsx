'use client';

import { useState } from 'react';
import { Play, Download, RotateCcw, CheckCircle, AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ProcessingProgress } from '@/components/ProgressIndicator';
import { cn } from '@/lib/utils';

interface FbaActionButtonsProps {
  canProcess: boolean;
  isProcessing: boolean;
  hasResult: boolean;
  onProcess: () => void;
  onDownload: () => void;
  onReset: () => void;
  processingProgress?: number;
  processingStatus?: string;
  estimatedTime?: number;
  currentPage?: number;
  totalPages?: number;
  statusMessage?: string;
  statusType?: 'info' | 'success' | 'error' | 'warning';
  className?: string;
}

export function FbaActionButtons({
  canProcess,
  isProcessing,
  hasResult,
  onProcess,
  onDownload,
  onReset,
  processingProgress = 0,
  processingStatus = '',
  estimatedTime,
  currentPage,
  totalPages,
  statusMessage,
  statusType = 'info',
  className
}: FbaActionButtonsProps) {
  const [downloadClicked, setDownloadClicked] = useState(false);
  const t = useTranslations('components.fbaActionButtons');

  const handleDownload = async () => {
    setDownloadClicked(true);
    await onDownload();
    setTimeout(() => setDownloadClicked(false), 2000);
  };

  const getStatusIcon = () => {
    switch (statusType) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-success-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-error-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-warning-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (statusType) {
      case 'success':
        return 'text-success-600 bg-success-50';
      case 'error':
        return 'text-error-600 bg-error-50';
      case 'warning':
        return 'text-warning-600 bg-warning-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card className={cn('h-fit', className)}>
      <CardContent className="p-4 space-y-4">
        {/* 处理进度 */}
        {isProcessing && (
          <ProcessingProgress
            progress={processingProgress}
            status={processingStatus}
            estimatedTime={estimatedTime}
            currentPage={currentPage}
            totalPages={totalPages}
          />
        )}

        {/* 操作按钮区域 */}
        <div className="space-y-3">
          {/* 桌面端 - 垂直布局（适合右侧栏） */}
          <div className="hidden md:flex flex-col gap-3">
            {/* 处理按钮 */}
            <Button
              onClick={onProcess}
              disabled={!canProcess || isProcessing}
              size="default"
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {t('processing')}
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  {t('processFile')}
                </>
              )}
            </Button>

            {/* 下载按钮 */}
            <Button
              onClick={handleDownload}
              disabled={!hasResult || isProcessing}
              variant="default"
              size="default"
            >
              {downloadClicked ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('downloaded')}
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  {t('downloadResult')}
                </>
              )}
            </Button>

            {/* 重置按钮 */}
            <Button
              onClick={onReset}
              variant="outline"
              size="default"
              disabled={isProcessing}
              className="w-full"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              {t('reset')}
            </Button>
          </div>

          {/* 移动端 - 网格布局 */}
          <div className="md:hidden space-y-3">
            {/* 主要操作按钮 */}
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={onProcess}
                disabled={!canProcess || isProcessing}
                size="default"
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('processing')}
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    {t('processFile')}
                  </>
                )}
              </Button>

              <Button
                onClick={handleDownload}
                disabled={!hasResult || isProcessing}
                variant="default"
                size="default"
              >
                {downloadClicked ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-1" />
                    {t('downloaded')}
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-1" />
                    {t('downloadResult')}
                  </>
                )}
              </Button>
            </div>

      
          </div>
        </div>

        {/* 状态信息 */}
        {/* {statusMessage && (
          <div className={cn(
            'flex items-center gap-2 p-3 rounded-md text-sm',
            getStatusColor()
          )}>
            {getStatusIcon()}
            <span className="flex-1">{statusMessage}</span>
          </div>
        )} */}

        {/* 操作提示 */}
        {!isProcessing && !hasResult && canProcess && (
          <div className="text-xs text-gray-500 text-center p-2 bg-gray-50 rounded">
            {t('processHint')}
          </div>
        )}

        {hasResult && !isProcessing && (
          <div className="text-xs text-success-600 text-center p-2 bg-success-50 rounded">
            {t('processCompleted')}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
