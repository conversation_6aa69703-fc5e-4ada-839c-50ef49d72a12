'use client';

import { FileText, Settings } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

interface FbaSettingsPanelProps {
  textToAdd: string;
  onTextChange: (text: string) => void;
  fontSize: number;
  onFontSizeChange: (size: number) => void;
  xOffset: number;
  onXOffsetChange: (offset: number) => void;
  yOffset: number;
  onYOffsetChange: (offset: number) => void;
  debugMode: boolean;
  onDebugModeChange: (enabled: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function FbaSettingsPanel({
  textToAdd,
  onTextChange,
  fontSize,
  onFontSizeChange,
  xOffset,
  onXOffsetChange,
  yOffset,
  onYOffsetChange,
  debugMode,
  onDebugModeChange,
  disabled = false,
  className
}: FbaSettingsPanelProps) {
  const t = useTranslations('components.fbaSettingsPanel');
  return (
    <Card className={cn('h-fit', className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-base">
          <FileText className="h-5 w-5 text-primary-500" />
          {t('textSettings')}
          <Settings className="h-4 w-4 text-gray-400 ml-1" />
          <span className="text-sm font-normal text-gray-500">{t('advancedOptions')}</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 文本设置区域 */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="text-input" className="text-sm font-medium text-gray-700">
              {t('addText')}
            </Label>
            <Input
              id="text-input"
              value={textToAdd}
              onChange={(e) => onTextChange(e.target.value)}
              placeholder="Made in China"
              disabled={disabled}
              className="text-sm"
            />
            <p className="text-xs text-gray-500">
              {t('textDescription')}
            </p>
          </div>
        </div>

        {/* 分割线 */}
        <div className="border-t border-gray-100" />

        {/* 高级设置区域 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Settings className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-700">{t('advancedOptionsLabel')}</span>
          </div>

          {/* 字体大小 */}
          <div className="space-y-2">
            <Label htmlFor="font-size" className="text-sm text-gray-600">
              {t('fontSize')}
            </Label>
            <div className="flex items-center space-x-2">
              <Input
                id="font-size"
                type="number"
                value={fontSize}
                onChange={(e) => onFontSizeChange(parseInt(e.target.value) || 8)}
                min="6"
                max="20"
                disabled={disabled}
                className="text-sm w-20"
              />
              <span className="text-xs text-gray-500">{t('fontSizeRange')}</span>
            </div>
          </div>

          {/* 位置调整 */}
          <div className="space-y-3">
            <Label className="text-sm text-gray-600">{t('positionAdjustment')}</Label>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor="x-offset" className="text-xs text-gray-500">
                  {t('horizontalOffset')}
                </Label>
                <Input
                  id="x-offset"
                  type="number"
                  value={xOffset}
                  onChange={(e) => onXOffsetChange(parseInt(e.target.value) || 0)}
                  disabled={disabled}
                  className="text-sm"
                  placeholder="0"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="y-offset" className="text-xs text-gray-500">
                  {t('verticalOffset')}
                </Label>
                <Input
                  id="y-offset"
                  type="number"
                  value={yOffset}
                  onChange={(e) => onYOffsetChange(parseInt(e.target.value) || 0)}
                  disabled={disabled}
                  className="text-sm"
                  placeholder="0"
                />
              </div>
            </div>
            <p className="text-xs text-gray-500">
              {t('positionDescription')}
            </p>
          </div>

          {/* 调试模式 */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="debug-mode"
                checked={debugMode}
                onCheckedChange={(checked) => onDebugModeChange(checked as boolean)}
                disabled={disabled}
              />
              <Label htmlFor="debug-mode" className="text-sm text-gray-600">
                {t('debugMode')}
              </Label>
            </div>
            <p className="text-xs text-gray-500 ml-6">
              {t('debugModeDescription')}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
