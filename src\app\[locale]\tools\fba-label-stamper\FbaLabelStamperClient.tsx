"use client";

import { useState, useEffect } from "react";
import { Tag, Eye } from "lucide-react";
import { useTranslations } from "next-intl";
import { TopNavigation } from "@/components/TopNavigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PdfViewer } from "@/components/PdfViewer";
import { DragDropUpload } from "@/components/DragDropUpload";
import { ProgressIndicator } from "@/components/ProgressIndicator";
import { FbaSettingsPanel } from "@/components/FbaSettingsPanel";
import { FbaActionButtons } from "@/components/FbaActionButtons";
import Quagga from 'quagga'

// PDF-lib 和 PDF.js 类型声明
declare global {
  interface Window {
    PDFLib: any;
    pdfjsLib: any;
  }
}

interface TextItem {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export default function FbaLabelStamperClient() {
  const t = useTranslations('tools.fbaLabelStamper');

  // 文件状态
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [processedPdfBytes, setProcessedPdfBytes] = useState<Uint8Array | null>(null);

  // 设置状态
  const [textToAdd, setTextToAdd] = useState("Made in China");
  const [fontSize, setFontSize] = useState(10);
  const [xOffset, setXOffset] = useState(2);
  const [yOffset, setYOffset] = useState(0);
  const [debugMode, setDebugMode] = useState(false);

  // 处理状态
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [processingStatus, setProcessingStatus] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState(0);

  // 系统状态
  const [isLibraryLoaded, setIsLibraryLoaded] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState("");
  const [statusType, setStatusType] = useState<'info' | 'success' | 'error' | 'warning'>('info');

  // 工作流步骤
  const [workflowSteps, setWorkflowSteps] = useState([
    { id: 'upload', label: '上传', completed: false, active: true },
    { id: 'process', label: '处理', completed: false, active: false },
    { id: 'preview', label: '预览', completed: false, active: false },
    { id: 'download', label: '下载', completed: false, active: false }
  ]);

  // 初始化加载库
  useEffect(() => {
    loadLibraries();
  }, []);

  // 更新工作流步骤
  const updateWorkflowStep = (stepId: string, completed: boolean, active: boolean) => {
    setWorkflowSteps(prev => prev.map(step =>
      step.id === stepId
        ? { ...step, completed, active }
        : step.id === 'upload' && stepId !== 'upload'
        ? { ...step, active: false }
        : step
    ));
  };

  // 设置状态消息
  const setStatus = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
    setStatusMessage(message);
    setStatusType(type);
  };

  // 加载必要的库
  const loadLibraries = async () => {
    if (isLibraryLoaded) return;

    try {
      setStatus(t('status.libraryLoading'), 'info');

      // 加载 PDF-lib
      if (!window.PDFLib) {
        const script1 = document.createElement('script');
        script1.src = '/libs/pdf-lib.min.js';
        document.head.appendChild(script1);
        await new Promise((resolve) => { script1.onload = resolve; });
      }

      // 加载 PDF.js
      if (!window.pdfjsLib) {
        const script2 = document.createElement('script');
        script2.src = '/libs/pdf.min.js';
        document.head.appendChild(script2);
        await new Promise((resolve) => { script2.onload = resolve; });

        // 设置 worker
        window.pdfjsLib.GlobalWorkerOptions.workerSrc = '/libs/pdf.worker.min.js';
      }

      setIsLibraryLoaded(true);
      setStatus(t('status.libraryLoaded'), 'success');
    } catch (error) {
      console.error('加载库失败:', error);
      setStatus(t('status.libraryFailed'), 'error');
    }
  };

  // 处理文件选择
  const handleFileSelect = async (file: File) => {
    setUploadStatus('uploading');
    setPdfFile(file);

    // 清除之前的处理结果
    setProcessedPdfBytes(null);

    // 更新工作流步骤
    updateWorkflowStep('upload', true, false);
    updateWorkflowStep('process', false, true);

    setUploadStatus('success');
    setStatus(t('upload.selected', {
      filename: file.name,
      size: (file.size / 1024 / 1024).toFixed(2)
    }), 'success');

    // 确保库已加载
    if (!isLibraryLoaded) {
      await loadLibraries();
    }
  };

  // 处理文件移除
  const handleFileRemove = () => {
    setPdfFile(null);
    setProcessedPdfBytes(null);
    setUploadStatus('idle');

    // 重置工作流步骤
    updateWorkflowStep('upload', false, true);
    updateWorkflowStep('process', false, false);
    updateWorkflowStep('preview', false, false);
    updateWorkflowStep('download', false, false);

    setStatus('', 'info');
  };

  // 动态加载QuaggaJS库
  // const loadQuaggaJS = async () => {
  //   if (typeof window !== 'undefined' && !(window as any).Quagga) {
  //     return new Promise((resolve, reject) => {
  //       const script = document.createElement('script');
  //       script.src = 'https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js';
  //       script.onload = () => resolve((window as any).Quagga);
  //       script.onerror = reject;
  //       document.head.appendChild(script);
  //     });
  //   }
  //   return (window as any).Quagga;
  // };

  // 使用QuaggaJS检测条形码
  const detectBarcodesWithQuagga = async (canvas: HTMLCanvasElement, viewport: any): Promise<any[]> => {

  const results = [];

    // 将 Canvas 转为 Data URL 以供 Quagga 识别
    const dataUrl = canvas.toDataURL();

    return new Promise((resolve) => {
      Quagga.decodeSingle({
        src: dataUrl,
        numOfWorkers: 0,           // 在主线程即可
        inputStream: {
          size: viewport.width,    // 与 Canvas 大小保持一致，提升定位精度
        },
        locator: {
          halfSample: false,       // 关闭二次采样，提高精度
          patchSize: 'medium',     // 查找窗口大小，可选 x-small, small, medium, large
        },
        decoder: {
          readers: [
            'code_128_reader',
            'ean_reader',
            'ean_8_reader',
            'upc_reader',
            // …根据需要添加
          ]
        },
        locate: true              // 打开定位
      }, (result) => {
        if (result && result.codeResult) {
          // result.boxes: 所有候选区域（四边形顶点数组）
          // result.box: 最终框（四个顶点）
          const box = result.box;
          const xs = box.map(p => p[0]), ys = box.map(p => p[1]);
          const minX = Math.min(...xs), maxX = Math.max(...xs);
          const minY = Math.min(...ys), maxY = Math.max(...ys);

          // 像素坐标 -> PDF pt 坐标
          const xPt = minX / scale;
          const yPt = pdfHeightPt - (maxY / scale);
          const widthPt  = (maxX - minX) / scale;
          const heightPt = (maxY - minY) / scale;

          results.push({
            page: pageNum,
            format: result.codeResult.format,
            text: result.codeResult.code,
            bbox: { x: xPt, y: yPt, width: widthPt, height: heightPt }
          });
        }
        canvas.remove();
        resolve(results);
      });
    });
  }


  // 主条形码检测函数
  const detectBarcodesInPDF = async (page: any, viewport: any) => {
    try {
      console.log('开始检测条形码...');

      // 1. 将PDF页面渲染到Canvas
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      if (!context) return [];

      canvas.width = viewport.width;
      canvas.height = viewport.height;

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      await page.render(renderContext).promise;
      console.log('PDF页面渲染完成');

      // 2. 使用QuaggaJS检测条形码
      const barcodes = await detectBarcodesWithQuagga(canvas, viewport);
      console.log('QuaggaJS检测结果:', barcodes);

      // 3. 转换坐标系（Canvas坐标 -> PDF坐标）
      const convertedBarcodes = barcodes.map((barcode: any) => ({
        ...barcode,
        x: barcode.x / viewport.scale,
        y: (viewport.height - barcode.y - barcode.height) / viewport.scale,
        width: barcode.width / viewport.scale,
        height: barcode.height / viewport.scale,
        bottomY: (viewport.height - barcode.bottomY) / viewport.scale
      }));

      console.log('转换后的条形码坐标:', convertedBarcodes);
      return convertedBarcodes;
    } catch (error) {
      console.log('条形码检测失败:', error);
      return [];
    }
  };

  // 从 PDF 中提取文本
  const extractTextFromPdf = async (arrayBuffer: ArrayBuffer): Promise<TextItem[][]> => {
    const CMAP_URL = '/libs/cmaps/';
    const CMAP_PACKED = true;

    const pdfDoc = await window.pdfjsLib.getDocument({ 
      data: arrayBuffer, 
      cMapUrl: CMAP_URL, 
      cMapPacked: CMAP_PACKED 
    }).promise;
    
    const allPagesTextItems: TextItem[][] = [];
    
    for (let i = 1; i <= pdfDoc.numPages; i++) {
      const page = await pdfDoc.getPage(i);
      const viewport = page.getViewport({ scale: 2.0 });
      const textContent = await page.getTextContent();

      // 检测条形码
      const barcodes = await detectBarcodesInPDF(page, viewport);
      console.log(`页面 ${i} 检测到 ${barcodes.length} 个条形码:`, barcodes);

      // 转换文本项目格式
      const items: TextItem[] = textContent.items.map((item: any) => ({
        text: item.str,
        x: item.transform[4],
        y: item.transform[5],
        width: item.width,
        height: item.height,
      }));
      
      // 合并同一行相邻文字块
      const merged: TextItem[] = [];
      let buffer: TextItem | null = null;
      
      for (const item of items) {
        if (buffer && Math.abs(item.y - buffer.y) < 2 && Math.abs(item.x - (buffer.x + buffer.width)) < 2) {
          buffer.text += item.text;
          buffer.width += item.width;
        } else {
          if (buffer) merged.push(buffer);
          buffer = { ...item };
        }
      }
      if (buffer) merged.push(buffer);
      
      allPagesTextItems.push(merged);
    }
    
    return allPagesTextItems;
  };

  // 在 PDF 中添加文本
  const addTextToPdf = async (
    arrayBuffer: ArrayBuffer,
    textItemsByPage: TextItem[][],
    textToAdd: string,
    fontSize: number,
    xOffset: number,
    yOffset: number
  ): Promise<{ pdfBytes: Uint8Array; processedCount: number }> => {
    const { PDFDocument, rgb, StandardFonts } = window.PDFLib;

    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const pages = pdfDoc.getPages();
    let processedCount = 0;

    for (let i = 0; i < pages.length; i++) {
      if (!textItemsByPage[i]) continue;

      const page = pages[i];
      let foundTarget = false;

      // 首先尝试查找"新品"
      for (const item of textItemsByPage[i]) {
        if (item.text.trim() === '新品') {
          const x = item.x + item.width + xOffset;
          const y = item.y + yOffset;

          page.drawText(textToAdd, {
            x,
            y,
            font: helveticaFont,
            size: fontSize,
            color: rgb(0, 0, 0) // 红色
          });

          // 如果开启调试模式，绘制调试框
          if (debugMode) {
            page.drawRectangle({
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              borderColor: rgb(0, 1, 0),
              borderWidth: 1
            });
          }

          foundTarget = true;
          processedCount++;
        }
      }

      // 如果没有找到"新品"，则尝试查找"New"
      if (!foundTarget) {
        for (const item of textItemsByPage[i]) {
          if (item.text.trim() === 'New') {
            const x = item.x + item.width + xOffset;
            const y = item.y + yOffset;

            page.drawText(textToAdd, {
              x,
              y,
              font: helveticaFont,
              size: fontSize,
              color: rgb(1, 0, 0) // 红色
            });

            // 如果开启调试模式，绘制调试框
            if (debugMode) {
              page.drawRectangle({
                x: item.x,
                y: item.y,
                width: item.width,
                height: item.height,
                borderColor: rgb(0, 1, 0),
                borderWidth: 1
              });
            }

            foundTarget = true;
            processedCount++;
          }
        }
      }
    }

    const pdfBytes = await pdfDoc.save();
    return { pdfBytes, processedCount };
  };

  const handleProcess = async () => {
    if (!pdfFile) {
      setStatus(t('status.selectFile'), 'warning');
      return;
    }

    if (!isLibraryLoaded) {
      setStatus(t('status.libraryLoading'), 'info');
      await loadLibraries();
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);
    setCurrentPage(0);
    setTotalPages(0);

    // 更新工作流步骤
    updateWorkflowStep('process', false, true);

    try {
      const arrayBuffer = await pdfFile.arrayBuffer();

      setProcessingStatus(t('status.analyzing'));
      setProcessingProgress(20);
      const textItemsByPage = await extractTextFromPdf(arrayBuffer.slice(0));

      // 设置总页数
      setTotalPages(textItemsByPage.length);

      setProcessingStatus(t('status.processing'));
      setProcessingProgress(40);

      const result = await addTextToPdf(
        arrayBuffer.slice(0),
        textItemsByPage,
        textToAdd,
        fontSize,
        xOffset,
        yOffset
      );

      setProcessingStatus(t('status.generating'));
      setProcessingProgress(80);

      // 保存处理后的 PDF 数据
      setProcessedPdfBytes(result.pdfBytes);
      setProcessingProgress(100);

      // 更新工作流步骤
      updateWorkflowStep('process', true, false);
      updateWorkflowStep('preview', false, true);

      setStatus(t('status.completed', { count: result.processedCount }), 'success');
    } catch (error) {
      console.error('处理过程中发生错误:', error);
      setStatus(t('status.failed', { error: error instanceof Error ? error.message : '未知错误' }), 'error');

      // 重置工作流步骤
      updateWorkflowStep('process', false, true);
    } finally {
      setIsProcessing(false);
      setProcessingProgress(0);
      setProcessingStatus('');
    }
  };

  // 下载处理后的 PDF
  const handleDownload = () => {
    if (!processedPdfBytes || !pdfFile) return;

    const blob = new Blob([processedPdfBytes], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `labeled_${pdfFile.name}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // 更新工作流步骤
    updateWorkflowStep('download', true, false);
    setStatus(t('download.completed'), 'success');
  };

  const handleReset = () => {
    setPdfFile(null);
    setTextToAdd("Made in China");
    setFontSize(8);
    setXOffset(2);
    setYOffset(0);
    setDebugMode(false);
    setProcessedPdfBytes(null);
    setUploadStatus('idle');

    // 重置工作流步骤
    setWorkflowSteps([
      { id: 'upload', label: '上传', completed: false, active: true },
      { id: 'process', label: '处理', completed: false, active: false },
      { id: 'preview', label: '预览', completed: false, active: false },
      { id: 'download', label: '下载', completed: false, active: false }
    ]);

    setStatus('', 'info');
  };

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />

      <main className="container mx-auto px-2 sm:px-4 py-4 sm:py-6 max-w-full">
        <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6 overflow-hidden">
          {/* 页面标题 */}
          <div className="text-center space-y-4">
            <h1 className="text-2xl md:text-3xl font-bold text-foreground flex items-center justify-center gap-2">
              <Tag className="h-6 w-6 md:h-8 md:w-8" />
              {t('title')}
            </h1>
            <p className="text-sm md:text-base text-muted-foreground max-w-2xl mx-auto">
              {t('subtitle')}
            </p>
          </div>

          {/* 响应式布局 */}
          <div className="space-y-4 sm:space-y-6 w-full">
            {/* 桌面端：左右两列布局，移动端：单栏布局 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6 w-full">

              {/* 移动端：操作按钮优先显示 */}
              <div className="lg:hidden order-1">
                <FbaActionButtons
                  canProcess={!!pdfFile && isLibraryLoaded}
                  isProcessing={isProcessing}
                  hasResult={!!processedPdfBytes}
                  onProcess={handleProcess}
                  onDownload={handleDownload}
                  onReset={handleReset}
                  processingProgress={processingProgress}
                  processingStatus={processingStatus}
                  estimatedTime={estimatedTime}
                  currentPage={currentPage}
                  totalPages={totalPages}
                  statusMessage={statusMessage}
                  statusType={statusType}
                />
              </div>

              {/* 左列：上传区域 + 预览区域 */}
              <div className="lg:col-span-2 space-y-3 sm:space-y-4 lg:space-y-6 order-2 lg:order-1 w-full min-w-0">
                {/* 上传区域 */}
                <DragDropUpload
                  onFileSelect={handleFileSelect}
                  onFileRemove={handleFileRemove}
                  currentFile={pdfFile}
                  isProcessing={isProcessing}
                  uploadStatus={uploadStatus}
                  maxSize={50}
                />

                {/* 预览区域 */}
                <Card className="h-fit hidden lg:block">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Eye className="h-5 w-5 text-primary-500" />
                      {t('preview.title')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="lg:min-h-[600px] w-full">
                      <PdfViewer
                        pdfFile={processedPdfBytes ? undefined : pdfFile}
                        pdfBytes={processedPdfBytes}
                        height="calc(100vh - 240px)"
                        loadingText={t('preview.loading')}
                        errorText={t('preview.error')}
                        noFileText={t('preview.noPreview')}
                        isProcessed={!!processedPdfBytes}
                        originalLabel={t('preview.original')}
                        processedLabel={t('preview.processed')}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 右列：操作按钮 + 设置面板（桌面端） */}
              <div className="lg:col-span-1 space-y-3 sm:space-y-4 order-3 lg:order-2 w-full min-w-0">
                {/* 操作按钮区域 - 桌面端显示 */}
                <div className="hidden lg:block">
                  <FbaActionButtons
                    canProcess={!!pdfFile && isLibraryLoaded}
                    isProcessing={isProcessing}
                    hasResult={!!processedPdfBytes}
                    onProcess={handleProcess}
                    onDownload={handleDownload}
                    onReset={handleReset}
                    processingProgress={processingProgress}
                    processingStatus={processingStatus}
                    estimatedTime={estimatedTime}
                    currentPage={currentPage}
                    totalPages={totalPages}
                    statusMessage={statusMessage}
                    statusType={statusType}
                  />
                </div>

                {/* 设置面板 */}
                <FbaSettingsPanel
                  textToAdd={textToAdd}
                  onTextChange={setTextToAdd}
                  fontSize={fontSize}
                  onFontSizeChange={setFontSize}
                  xOffset={xOffset}
                  onXOffsetChange={setXOffset}
                  yOffset={yOffset}
                  onYOffsetChange={setYOffset}
                  debugMode={debugMode}
                  onDebugModeChange={setDebugMode}
                  disabled={isProcessing}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
