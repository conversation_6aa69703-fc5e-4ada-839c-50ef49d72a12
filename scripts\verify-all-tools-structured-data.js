#!/usr/bin/env node

const axios = require('axios');
const cheerio = require('cheerio');

// 工具配置 - 所有已实现的工具
const toolConfigs = [
  { id: 'image-compressor', path: '/tools/image-compressor', name: '图片压缩器' },
  { id: 'qr-generator', path: '/tools/qr-generator', name: '二维码生成器' },
  { id: 'json-formatter', path: '/tools/json-formatter', name: 'JSON 格式化' },
  { id: 'url-encoder', path: '/tools/url-encoder', name: 'URL 编码解码' },
  { id: 'timestamp-converter', path: '/tools/timestamp-converter', name: '时间戳转换' },
  { id: 'uuid-generator', path: '/tools/uuid-generator', name: 'UUID 生成器' },
  { id: 'password-generator', path: '/tools/password-generator', name: '密码生成器' },
  { id: 'hash-calculator', path: '/tools/hash-calculator', name: '哈希计算器' },
  { id: 'unit-converter', path: '/tools/unit-converter', name: '单位转换器' },
  { id: 'pdf-merger', path: '/tools/pdf-merger', name: 'PDF 合并器' },
  { id: 'pdf-splitter', path: '/tools/pdf-splitter', name: 'PDF 分割器' },
  { id: 'pdf-to-image', path: '/tools/pdf-to-image', name: 'PDF 转图片' },
  { id: 'fba-label-stamper', path: '/tools/fba-label-stamper', name: 'FBA 标签工具' }
];

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3002';
const LOCALE = 'zh';

// 验证单个工具页面的结构化数据
async function verifyToolStructuredData(tool) {
  const url = `${BASE_URL}/${LOCALE}${tool.path}`;
  
  console.log(`\n🔍 检查工具: ${tool.name} (${tool.id})`);
  console.log(`📄 URL: ${url}`);
  
  try {
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; StructuredDataBot/1.0)'
      }
    });
    
    const $ = cheerio.load(response.data);
    const structuredDataScripts = $('script[type="application/ld+json"]');
    
    if (structuredDataScripts.length === 0) {
      console.log(`❌ 未发现结构化数据`);
      return {
        tool: tool.id,
        success: false,
        error: '未发现结构化数据',
        schemas: []
      };
    }
    
    console.log(`✅ 发现 ${structuredDataScripts.length} 个结构化数据块`);
    
    const schemas = [];
    let hasSoftwareApplication = false;
    let hasBreadcrumbList = false;
    let hasWebSite = false;
    let hasOrganization = false;
    
    structuredDataScripts.each((index, element) => {
      try {
        const jsonText = $(element).html();
        const data = JSON.parse(jsonText);
        schemas.push(data);
        
        const schemaType = data['@type'];
        console.log(`  📊 Schema ${index + 1}: ${schemaType} - ${data.name || '未命名'}`);
        
        // 检查必需的 Schema 类型
        if (schemaType === 'SoftwareApplication') {
          hasSoftwareApplication = true;
          
          // 验证必需字段
          const requiredFields = ['name', 'description', 'url', 'applicationCategory'];
          const missingFields = requiredFields.filter(field => !data[field]);
          
          if (missingFields.length > 0) {
            console.log(`    ⚠️  缺少必需字段: ${missingFields.join(', ')}`);
          } else {
            console.log(`    ✅ 所有必需字段完整`);
          }
          
          // 检查是否有无效属性
          if (data.browserRequirements) {
            console.log(`    ❌ 发现无效属性: browserRequirements`);
          }
          if (data.permissions) {
            console.log(`    ❌ 发现无效属性: permissions`);
          }
        } else if (schemaType === 'BreadcrumbList') {
          hasBreadcrumbList = true;
          const itemCount = data.itemListElement?.length || 0;
          console.log(`    📍 包含 ${itemCount} 个面包屑项`);
        } else if (schemaType === 'WebSite') {
          hasWebSite = true;
        } else if (schemaType === 'Organization') {
          hasOrganization = true;
        }
        
      } catch (parseError) {
        console.log(`    ❌ JSON 解析错误: ${parseError.message}`);
      }
    });
    
    // 验证结果总结
    const validationResults = [];
    if (!hasSoftwareApplication) validationResults.push('缺少 SoftwareApplication Schema');
    if (!hasBreadcrumbList) validationResults.push('缺少 BreadcrumbList Schema');
    
    if (validationResults.length === 0) {
      console.log(`✅ 验证通过: 所有必需的 Schema 都存在`);
    } else {
      console.log(`⚠️  验证警告: ${validationResults.join(', ')}`);
    }
    
    return {
      tool: tool.id,
      success: true,
      schemas: schemas,
      hasSoftwareApplication,
      hasBreadcrumbList,
      hasWebSite,
      hasOrganization,
      validationResults
    };
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    return {
      tool: tool.id,
      success: false,
      error: error.message,
      schemas: []
    };
  }
}

// 生成验证报告
function generateReport(results) {
  console.log('\n' + '='.repeat(80));
  console.log('📊 结构化数据验证报告');
  console.log('='.repeat(80));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  const withSoftwareApp = successful.filter(r => r.hasSoftwareApplication);
  const withBreadcrumbs = successful.filter(r => r.hasBreadcrumbList);
  
  console.log(`\n📈 总体统计:`);
  console.log(`  总工具数: ${results.length}`);
  console.log(`  成功检查: ${successful.length}`);
  console.log(`  检查失败: ${failed.length}`);
  console.log(`  有 SoftwareApplication: ${withSoftwareApp.length}`);
  console.log(`  有 BreadcrumbList: ${withBreadcrumbs.length}`);
  
  if (failed.length > 0) {
    console.log(`\n❌ 检查失败的工具:`);
    failed.forEach(result => {
      console.log(`  - ${result.tool}: ${result.error}`);
    });
  }
  
  const incomplete = successful.filter(r => !r.hasSoftwareApplication || !r.hasBreadcrumbList);
  if (incomplete.length > 0) {
    console.log(`\n⚠️  结构化数据不完整的工具:`);
    incomplete.forEach(result => {
      const missing = [];
      if (!result.hasSoftwareApplication) missing.push('SoftwareApplication');
      if (!result.hasBreadcrumbList) missing.push('BreadcrumbList');
      console.log(`  - ${result.tool}: 缺少 ${missing.join(', ')}`);
    });
  }
  
  const complete = successful.filter(r => r.hasSoftwareApplication && r.hasBreadcrumbList);
  if (complete.length > 0) {
    console.log(`\n✅ 结构化数据完整的工具:`);
    complete.forEach(result => {
      console.log(`  - ${result.tool}: 完整 (${result.schemas.length} 个 Schema)`);
    });
  }
  
  console.log(`\n🎯 完成度: ${complete.length}/${results.length} (${Math.round(complete.length / results.length * 100)}%)`);
}

// 主函数
async function main() {
  console.log('🚀 开始验证所有工具页面的结构化数据...');
  console.log(`🌐 基础URL: ${BASE_URL}`);
  console.log(`🌍 语言: ${LOCALE}`);
  console.log(`📋 待检查工具数: ${toolConfigs.length}`);
  
  const results = [];
  
  for (const tool of toolConfigs) {
    const result = await verifyToolStructuredData(tool);
    results.push(result);
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  generateReport(results);
  
  console.log('\n🎉 验证完成！');
  console.log('\n📋 建议的下一步操作:');
  console.log('1. 修复检查失败的工具页面');
  console.log('2. 为缺少结构化数据的工具运行批量添加脚本');
  console.log('3. 使用 Google Rich Results Test 进一步验证');
  console.log('4. 部署到生产环境并监控 SEO 效果');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { verifyToolStructuredData, generateReport, toolConfigs };
