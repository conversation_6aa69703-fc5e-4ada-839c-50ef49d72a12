"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Copy, Download, Upload, Check, AlertCircle, Info } from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

export default function JsonFormatterClient() {
  const t = useTranslations('tools.jsonFormatter');
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [error, setError] = useState("");
  const [copied, setCopied] = useState(false);
  const [indentSize, setIndentSize] = useState(2);

  const formatJson = () => {
    try {
      const parsed = JSON.parse(input);
      const formatted = JSON.stringify(parsed, null, indentSize);
      setOutput(formatted);
      setError("");
    } catch (err) {
      setError(err instanceof Error ? err.message : t('result.invalid'));
      setOutput("");
    }
  };

  const minifyJson = () => {
    try {
      const parsed = JSON.parse(input);
      const minified = JSON.stringify(parsed);
      setOutput(minified);
      setError("");
    } catch (err) {
      setError(err instanceof Error ? err.message : t('result.invalid'));
      setOutput("");
    }
  };

  const validateJson = () => {
    try {
      JSON.parse(input);
      setError("");
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : t('result.invalid'));
      return false;
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(output);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("复制失败:", err);
    }
  };

  const downloadJson = () => {
    const blob = new Blob([output], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "formatted.json";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const loadFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setInput(content);
      };
      reader.readAsText(file);
    }
  };

  const clearAll = () => {
    setInput("");
    setOutput("");
    setError("");
  };

  const sampleJson = `{
  "name": "示例数据",
  "version": "1.0.0",
  "users": [
    {
      "id": 1,
      "name": "张三",
      "email": "<EMAIL>",
      "active": true
    },
    {
      "id": 2,
      "name": "李四",
      "email": "<EMAIL>",
      "active": false
    }
  ],
  "settings": {
    "theme": "dark",
    "language": "zh-CN",
    "notifications": {
      "email": true,
      "push": false
    }
  }
}`;

  const loadSample = () => {
    setInput(sampleJson);
  };

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('subtitle')}
            </p>
          </div>

          {/* Controls */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>{t('actions.title')}</CardTitle>
              <CardDescription>{t('actions.description')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 items-center">
                <Button onClick={formatJson} disabled={!input.trim()}>
                  {t('actions.format')}
                </Button>
                <Button onClick={minifyJson} variant="outline" disabled={!input.trim()}>
                  {t('actions.minify')}
                </Button>
                <Button onClick={validateJson} variant="outline" disabled={!input.trim()}>
                  {t('actions.validate')}
                </Button>
                <Button onClick={loadSample} variant="outline">
                  {t('actions.loadSample')}
                </Button>
                <Button onClick={clearAll} variant="outline">
                  {t('actions.clear')}
                </Button>

                <div className="flex items-center gap-2 ml-auto">
                  <label className="text-sm font-medium">{t('actions.indentSize')}:</label>
                  <select
                    value={indentSize}
                    onChange={(e) => setIndentSize(parseInt(e.target.value))}
                    className="px-3 py-1 border rounded-md text-sm"
                  >
                    <option value={2}>2 {t('actions.spaces')}</option>
                    <option value={4}>4 {t('actions.spaces')}</option>
                    <option value={8}>8 {t('actions.spaces')}</option>
                  </select>
                </div>

                <Button asChild variant="outline" size="sm">
                  <label>
                    <Upload className="h-4 w-4 mr-2" />
                    {t('actions.upload')}
                    <input
                      type="file"
                      accept=".json,.txt"
                      onChange={loadFile}
                      className="hidden"
                    />
                  </label>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Input/Output */}
          <div className="grid lg:grid-cols-2 gap-6 mb-6">
            {/* Input */}
            <Card>
              <CardHeader>
                <CardTitle>{t('input.title')}</CardTitle>
                <CardDescription>{t('input.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder={t('input.placeholder')}
                  className="min-h-[400px] font-mono text-sm"
                />
                {error && (
                  <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                    <div className="flex items-center gap-2 text-destructive">
                      <AlertCircle className="h-4 w-4" />
                      <span className="font-medium">{t('result.error')}</span>
                    </div>
                    <p className="text-sm mt-1 text-destructive/80">{error}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Output */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{t('result.title')}</CardTitle>
                    <CardDescription>{t('result.description')}</CardDescription>
                  </div>
                  {output && (
                    <div className="flex gap-2">
                      <Button
                        onClick={copyToClipboard}
                        size="sm"
                        variant="outline"
                        disabled={!output}
                      >
                        {copied ? (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            {t('actions.copied')}
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 mr-2" />
                            {t('actions.copy')}
                          </>
                        )}
                      </Button>
                      <Button
                        onClick={downloadJson}
                        size="sm"
                        variant="outline"
                        disabled={!output}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        {t('actions.download')}
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={output}
                  readOnly
                  placeholder={t('result.placeholder')}
                  className="min-h-[400px] font-mono text-sm"
                />
              </CardContent>
            </Card>
          </div>

          {/* Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}