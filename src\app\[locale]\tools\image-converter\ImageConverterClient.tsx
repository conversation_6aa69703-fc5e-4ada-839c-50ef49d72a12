"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Upload, Download, RefreshCw, Info, Image as ImageIcon, X, Settings } from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import imageCompression from "browser-image-compression";

interface ConvertedImage {
  id: string;
  original: File;
  converted: Blob;
  url: string;
  originalFormat: string;
  targetFormat: string;
  originalSize: number;
  convertedSize: number;
}

const supportedFormats = ['jpeg', 'jpg', 'png', 'webp', 'bmp', 'gif'] as const;
const outputFormats = ['jpeg', 'png', 'webp', 'bmp'] as const;

export default function ImageConverterClient() {
  const t = useTranslations('tools.imageConverter');
  
  const [images, setImages] = useState<ConvertedImage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [targetFormat, setTargetFormat] = useState<typeof outputFormats[number]>('jpeg');
  const [quality, setQuality] = useState(90);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);

  // 验证文件格式
  const validateFile = (file: File): boolean => {
    const fileType = file.type.toLowerCase();
    const fileName = file.name.toLowerCase();
    
    const isValidMimeType = supportedFormats.some(format => 
      fileType.includes(format) || fileType === `image/${format}`
    );
    const isValidExtension = supportedFormats.some(format => 
      fileName.endsWith(`.${format}`)
    );
    
    return isValidMimeType || isValidExtension;
  };

  // 图片格式转换函数
  const convertImageFormat = async (
    file: File,
    format: string,
    quality: number
  ): Promise<{ blob: Blob; size: number }> => {
    try {
      // 设置压缩选项
      const options = {
        maxSizeMB: 20,
        maxWidthOrHeight: 4096,
        useWebWorker: true,
        fileType: `image/${format}`,
        initialQuality: format === 'jpeg' ? quality / 100 : 0.8
      };

      // 使用 browser-image-compression 库进行转换
      const compressedFile = await imageCompression(file, options);
      
      // 如果格式相同且质量设置不需要调整，直接返回原文件
      if (file.type === `image/${format}` && format !== 'jpeg') {
        return { blob: file, size: file.size };
      }

      return { blob: compressedFile, size: compressedFile.size };
    } catch (error) {
      // 如果库转换失败，回退到 Canvas 方法
      return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          
          if (ctx) {
            // 为 JPEG 格式设置白色背景
            if (format === 'jpeg') {
              ctx.fillStyle = '#FFFFFF';
              ctx.fillRect(0, 0, canvas.width, canvas.height);
            }
            
            ctx.drawImage(img, 0, 0);
          }

          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve({ blob, size: blob.size });
              } else {
                reject(new Error('转换失败'));
              }
            },
            `image/${format}`,
            format === 'jpeg' ? quality / 100 : undefined
          );
        };

        img.onerror = () => reject(new Error('图片加载失败'));
        img.src = URL.createObjectURL(file);
      });
    }
  };

  // 处理文件上传
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    setIsProcessing(true);
    setError(null);
    const newImages: ConvertedImage[] = [];

    for (const file of Array.from(files)) {
      try {
        // 验证文件
        if (!validateFile(file)) {
          throw new Error(`不支持的文件格式: ${file.name}`);
        }

        // 检查文件大小 (20MB 限制)
        if (file.size > 20 * 1024 * 1024) {
          throw new Error(`文件过大: ${file.name} (最大 20MB)`);
        }

        const { blob: convertedBlob, size: convertedSize } = await convertImageFormat(
          file, 
          targetFormat, 
          quality
        );
        const url = URL.createObjectURL(convertedBlob);

        newImages.push({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          original: file,
          converted: convertedBlob,
          url,
          originalFormat: file.type,
          targetFormat: `image/${targetFormat}`,
          originalSize: file.size,
          convertedSize
        });
      } catch (error) {
        console.error('转换失败:', error);
        setError(error instanceof Error ? error.message : '转换失败');
      }
    }

    setImages(prev => [...prev, ...newImages]);
    setIsProcessing(false);
  }, [targetFormat, quality]);

  // 下载单个文件
  const downloadImage = (image: ConvertedImage) => {
    const a = document.createElement('a');
    a.href = image.url;
    const originalName = image.original.name.split('.').slice(0, -1).join('.');
    a.download = `${originalName}.${targetFormat}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // 批量下载
  const downloadAll = async () => {
    for (const image of images) {
      await new Promise(resolve => setTimeout(resolve, 100)); // 短暂延迟避免浏览器阻止
      downloadImage(image);
    }
  };

  // 删除图片
  const removeImage = (id: string) => {
    setImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.url);
      }
      return prev.filter(img => img.id !== id);
    });
  };

  // 清空所有图片
  const clearAll = () => {
    images.forEach(img => URL.revokeObjectURL(img.url));
    setImages([]);
    setError(null);
  };

  // 拖拽处理
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 清理资源
  useEffect(() => {
    return () => {
      images.forEach(img => URL.revokeObjectURL(img.url));
    };
  }, [images]);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2 text-gray-900">{t('title')}</h1>
            <p className="text-gray-600">{t('subtitle')}</p>
          </div>

          {/* 主要内容区域 - 左右布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧：设置和上传区域 */}
            <div className="lg:col-span-1 space-y-6">
              {/* 设置区域 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Settings className="h-5 w-5" />
                    {t('settings.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="format">{t('settings.format')}</Label>
                    <select 
                      id="format"
                      value={targetFormat} 
                      onChange={(e) => setTargetFormat(e.target.value as typeof outputFormats[number])}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {outputFormats.map(format => (
                        <option key={format} value={format}>
                          {format.toUpperCase()}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  {targetFormat === 'jpeg' && (
                    <div>
                      <Label htmlFor="quality">{t('settings.quality')}: {quality}%</Label>
                      <input
                        id="quality"
                        type="range"
                        min="10"
                        max="100"
                        value={quality}
                        onChange={(e) => setQuality(Number(e.target.value))}
                        className="w-full mt-1"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 上传区域 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Upload className="h-5 w-5" />
                    {t('upload.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div
                    className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                      dragActive 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <ImageIcon className="mx-auto h-10 w-10 text-gray-400 mb-3" />
                    <p className="text-sm font-medium text-gray-900 mb-2">
                      {t('upload.dragText')}
                    </p>
                    <p className="text-xs text-gray-600 mb-4">
                      {t('upload.supportedFormats')}
                    </p>
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isProcessing}
                      size="sm"
                      className="w-full"
                    >
                      {isProcessing ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          {t('upload.processing')}
                        </>
                      ) : (
                        <>
                          <Upload className="mr-2 h-4 w-4" />
                          {t('upload.selectFiles')}
                        </>
                      )}
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      accept={supportedFormats.map(f => `image/${f}`).join(',')}
                      onChange={(e) => handleFileSelect(e.target.files)}
                      className="hidden"
                    />
                  </div>
                  
                  {error && (
                    <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
                      {error}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 使用说明 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Info className="h-5 w-5" />
                    {t('info.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-gray-600">
                    {t.raw('info.items').map((item: string, index: number) => (
                      <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* 右侧：转换结果 */}
            <div className="lg:col-span-2">
              {images.length > 0 ? (
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center gap-2">
                        <Download className="h-5 w-5" />
                        {t('results.title')} ({images.length})
                      </CardTitle>
                      <div className="flex gap-2">
                        <Button onClick={downloadAll} variant="outline" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          {t('results.downloadAll')}
                        </Button>
                        <Button onClick={clearAll} variant="outline" size="sm">
                          <X className="mr-2 h-4 w-4" />
                          {t('results.clearAll')}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {images.map((image) => (
                        <div key={image.id} className="border rounded-lg p-4 bg-white">
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 truncate text-sm">
                                {image.original.name}
                              </h3>
                              <p className="text-xs text-gray-600">
                                {image.originalFormat.replace('image/', '').toUpperCase()} → {image.targetFormat.replace('image/', '').toUpperCase()}
                              </p>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeImage(image.id)}
                              className="ml-2"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                          
                          <div className="mb-3">
                            <img
                              src={image.url}
                              alt={image.original.name}
                              className="w-full h-32 object-cover rounded border"
                            />
                          </div>
                          
                          <div className="flex justify-between items-center text-xs text-gray-600 mb-3">
                            <span>{formatFileSize(image.originalSize)}</span>
                            <span>→</span>
                            <span className={image.convertedSize < image.originalSize ? 'text-green-600' : 'text-gray-600'}>
                              {formatFileSize(image.convertedSize)}
                            </span>
                          </div>
                          
                          <Button
                            onClick={() => downloadImage(image)}
                            className="w-full"
                            size="sm"
                          >
                            <Download className="mr-2 h-4 w-4" />
                            {t('results.download')}
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="flex items-center justify-center h-64 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-center">
                    <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600">转换结果将在此处显示</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}