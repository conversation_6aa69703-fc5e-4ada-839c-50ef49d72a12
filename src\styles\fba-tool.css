/* FBA 标签工具自定义样式 */

/* 拖拽上传区域动画 */
.drag-drop-area {
  transition: all 0.2s ease-in-out;
}

.drag-drop-area:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.drag-over {
  border-color: rgb(59 130 246);
  background-color: rgb(239 246 255);
  transform: scale(1.02);
}

/* 按钮悬停效果 */
.btn-hover-lift {
  transition: all 0.2s ease;
}

.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

/* 卡片动画 */
.card-enter {
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 0.3s ease forwards;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条动画 */
.progress-bar {
  transition: width 0.3s ease-out;
}

/* 状态指示器 */
.status-success {
  color: rgb(34 197 94);
  background-color: rgb(240 253 244);
}

.status-error {
  color: rgb(239 68 68);
  background-color: rgb(254 242 242);
}

.status-warning {
  color: rgb(234 179 8);
  background-color: rgb(255 251 235);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mobile-stack {
    flex-direction: column;
  }
  
  .mobile-full-width {
    width: 100%;
  }
  
  .mobile-spacing {
    margin-bottom: 1rem;
  }
}

/* 加载动画 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 文件信息卡片 */
.file-info-card {
  border: 1px solid rgb(229 231 235);
  border-radius: 8px;
  padding: 16px;
  background: white;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: all 0.2s ease;
}

.file-info-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* 工作流进度点 */
.workflow-step {
  transition: all 0.2s ease;
}

.workflow-step.active {
  transform: scale(1.1);
}

.workflow-step.completed {
  background-color: rgb(59 130 246);
  color: white;
}

/* 预览区域优化 */
.pdf-preview-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* 设置面板样式 */
.settings-panel {
  background: rgb(249 250 251);
  border-radius: 8px;
  padding: 16px;
}

.settings-section {
  border-bottom: 1px solid rgb(229 231 235);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
  }
}

/* 状态消息样式 */
.status-message {
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgb(17 24 39);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}

/* 平滑滚动 */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* 焦点样式 */
.focus-ring:focus {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
}

/* 无障碍访问 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
