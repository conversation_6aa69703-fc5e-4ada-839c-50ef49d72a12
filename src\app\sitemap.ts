import { MetadataRoute } from 'next';
import { locales } from '@/i18n/config';
import { tools } from '@/config/tools';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';

  // 从配置文件获取所有已实现的工具
  const implementedTools = tools.filter(tool => tool.implemented !== false);

  const sitemap: MetadataRoute.Sitemap = [];

  // 生成多语言 alternates 的辅助函数
  const generateAlternates = (path: string = '') => {
    const alternates: Record<string, string> = {};

    locales.forEach(locale => {
      const url = `${baseUrl}/${locale}${path}`;
      alternates[locale] = url;

      // 为中文添加额外的语言代码
      if (locale === 'zh') {
        alternates['zh-CN'] = url;
      }
      // 为英文添加额外的语言代码
      if (locale === 'en') {
        alternates['en-US'] = url;
      }
    });

    // 设置默认语言
    alternates['x-default'] = `${baseUrl}/zh${path}`;

    return alternates;
  };

  // 为每种语言生成页面
  locales.forEach((locale) => {
    // 首页
    sitemap.push({
      url: `${baseUrl}/${locale}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
      alternates: {
        languages: generateAlternates()
      }
    });

    // 工具页面 - 使用配置文件中的工具
    implementedTools.forEach((tool) => {
      sitemap.push({
        url: `${baseUrl}/${locale}${tool.path}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
        alternates: {
          languages: generateAlternates(tool.path)
        }
      });
    });
  });

  return sitemap;
}
