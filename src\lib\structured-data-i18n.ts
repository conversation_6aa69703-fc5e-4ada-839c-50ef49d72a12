import { Locale } from '@/i18n/config';

// 语言代码映射 - 符合 Schema.org 标准
export const schemaLanguageMap: Record<Locale, string> = {
  zh: 'zh-CN',
  en: 'en-US', 
  ja: 'ja-JP',
  de: 'de-DE',
  ru: 'ru-RU'
};

// 语言名称映射 - 用于 availableLanguage 字段
export const schemaLanguageNames: Record<Locale, string> = {
  zh: 'Chinese',
  en: 'English',
  ja: 'Japanese', 
  de: 'German',
  ru: 'Russian'
};

// 获取 Schema.org 语言代码
export function getSchemaLanguage(locale: Locale): string {
  return schemaLanguageMap[locale] || schemaLanguageMap.en;
}

// 获取所有支持的语言名称列表
export function getAllSchemaLanguages(): string[] {
  return Object.values(schemaLanguageNames);
}

// 结构化数据翻译键映射
export const structuredDataTranslationKeys = {
  website: {
    alternateName: 'structuredData.website.alternateName',
    description: 'structuredData.website.description'
  },
  organization: {
    description: 'structuredData.organization.description',
    contactType: 'structuredData.organization.contactType'
  },
  software: {
    requirements: 'structuredData.software.requirements',
    features: {
      freeToUse: 'structuredData.software.features.freeToUse',
      noRegistration: 'structuredData.software.features.noRegistration', 
      browserBased: 'structuredData.software.features.browserBased',
      privacySecure: 'structuredData.software.features.privacySecure'
    }
  },
  itemList: {
    toolsCollection: 'structuredData.itemList.toolsCollection',
    toolsDescription: 'structuredData.itemList.toolsDescription'
  },
  navigation: {
    home: 'navigation.home'
  }
} as const;
