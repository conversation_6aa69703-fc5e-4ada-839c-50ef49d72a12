#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 工具页面路径
const toolsDir = 'src/app/[locale]/tools';

// 需要添加SEO优化的工具列表
const toolsToOptimize = [
  'uuid-generator',
  'timestamp-converter', 
  'hash-calculator',
  'unit-converter',
  'password-generator',
  'url-encoder',
  'json-formatter',
  'pdf-merger',
  'pdf-splitter',
  'pdf-to-image',
  'image-compressor',
  'fba-label-stamper'
];

// 检查文件是否需要添加ToolPageLayout
function needsOptimization(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return !content.includes('ToolPageLayout') && !content.includes('Breadcrumb');
}

// 添加必要的导入
function addImports(content) {
  const imports = [
    'import { useLocale } from "next-intl";',
    'import { ToolPageLayout } from "@/components/ToolPageLayout";',
    'import { getToolById } from "@/config/tools";'
  ];
  
  // 找到最后一个import语句的位置
  const lines = content.split('\n');
  let lastImportIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ')) {
      lastImportIndex = i;
    }
  }
  
  if (lastImportIndex !== -1) {
    // 在最后一个import后添加新的imports
    imports.reverse().forEach(importStatement => {
      if (!content.includes(importStatement)) {
        lines.splice(lastImportIndex + 1, 0, importStatement);
        lastImportIndex++;
      }
    });
  }
  
  return lines.join('\n');
}

// 添加工具信息获取代码
function addToolInfo(content, toolId) {
  const lines = content.split('\n');
  
  // 找到组件函数的开始
  const componentStartRegex = /export default function \w+Client\(\) \{/;
  let componentStartIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    if (componentStartRegex.test(lines[i])) {
      componentStartIndex = i;
      break;
    }
  }
  
  if (componentStartIndex !== -1) {
    // 找到第一个useTranslations调用
    let translationsIndex = -1;
    for (let i = componentStartIndex; i < lines.length; i++) {
      if (lines[i].includes('useTranslations(')) {
        translationsIndex = i;
        break;
      }
    }
    
    if (translationsIndex !== -1) {
      // 在useTranslations后添加工具信息代码
      const toolInfoCode = [
        '  const locale = useLocale();',
        '  ',
        '  // 获取工具信息',
        `  const tool = getToolById('${toolId}')!;`,
        '  const toolName = t(\'name\');',
        '  const toolDescription = t(\'description\');'
      ];
      
      // 检查是否已经存在这些代码
      const hasToolInfo = lines.some(line => line.includes('getToolById'));
      if (!hasToolInfo) {
        toolInfoCode.reverse().forEach(code => {
          lines.splice(translationsIndex + 1, 0, code);
        });
      }
    }
  }
  
  return lines.join('\n');
}

// 更新返回的JSX，使用ToolPageLayout
function updateJSX(content) {
  // 替换TopNavigation和页面结构
  let updatedContent = content;
  
  // 查找并替换return语句中的结构
  const returnRegex = /return \(\s*<div className="min-h-screen[^>]*>\s*<TopNavigation \/>/;
  if (returnRegex.test(updatedContent)) {
    updatedContent = updatedContent.replace(
      returnRegex,
      'return (\n    <>\n      <TopNavigation />\n      \n      <ToolPageLayout\n        tool={tool}\n        locale={locale}\n        toolName={toolName}\n        toolDescription={toolDescription}\n      >'
    );
    
    // 替换结尾的div结构
    updatedContent = updatedContent.replace(
      /<\/main>\s*<\/div>\s*\);/,
      '</ToolPageLayout>\n    </>\n  );'
    );
    
    // 移除重复的标题部分
    updatedContent = updatedContent.replace(
      /\{\/\* Header \*\/\}\s*<div className="mb-8">\s*<h1[^>]*>.*?<\/h1>\s*<p[^>]*>\s*.*?\s*<\/p>\s*<\/div>/s,
      ''
    );
  }
  
  return updatedContent;
}

// 处理单个工具文件
function optimizeToolFile(toolName) {
  const clientFilePath = path.join(toolsDir, toolName, `${toPascalCase(toolName)}Client.tsx`);
  
  if (!fs.existsSync(clientFilePath)) {
    console.log(`❌ 文件不存在: ${clientFilePath}`);
    return;
  }
  
  if (!needsOptimization(clientFilePath)) {
    console.log(`✅ ${toolName} 已经优化过了`);
    return;
  }
  
  console.log(`🔧 正在优化: ${toolName}`);
  
  try {
    let content = fs.readFileSync(clientFilePath, 'utf8');
    
    // 1. 添加导入
    content = addImports(content);
    
    // 2. 添加工具信息获取代码
    content = addToolInfo(content, toolName);
    
    // 3. 更新JSX结构
    content = updateJSX(content);
    
    // 写回文件
    fs.writeFileSync(clientFilePath, content);
    console.log(`✅ 优化完成: ${toolName}`);
    
  } catch (error) {
    console.error(`❌ 优化失败 ${toolName}:`, error.message);
  }
}

// 转换为PascalCase
function toPascalCase(str) {
  return str
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

// 主函数
function main() {
  console.log('🚀 开始为工具页面添加SEO优化...\n');
  
  toolsToOptimize.forEach(toolName => {
    optimizeToolFile(toolName);
  });
  
  console.log('\n✅ SEO优化完成！');
  console.log('\n📋 优化内容包括：');
  console.log('- 添加结构化数据（Schema.org）');
  console.log('- 添加面包屑导航');
  console.log('- 优化页面标题和描述');
  console.log('- 统一页面布局结构');
}

if (require.main === module) {
  main();
}
