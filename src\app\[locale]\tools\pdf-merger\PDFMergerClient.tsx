"use client";

import { useState, useCallback } from "react";
import { Upload, Download, FileStack, Trash2, Arrow<PERSON><PERSON>, ArrowDown, Info } from "lucide-react";
import { PDFDocument } from "pdf-lib";
import { useTranslations } from 'next-intl';
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface PDFFile {
  id: string;
  file: File;
  name: string;
  size: number;
  pageCount?: number;
}

export default function PDFMergerClient() {
  const t = useTranslations('tools.pdfMerger');
  const [pdfFiles, setPdfFiles] = useState<PDFFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [mergedPdfUrl, setMergedPdfUrl] = useState<string | null>(null);

  const handleFileSelect = useCallback(async (files: FileList) => {
    const newFiles: PDFFile[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (file.type !== 'application/pdf') {
        continue;
      }

      try {
        // 读取 PDF 文件获取页数
        const arrayBuffer = await file.arrayBuffer();
        const pdfDoc = await PDFDocument.load(arrayBuffer);
        const pageCount = pdfDoc.getPageCount();

        const pdfFile: PDFFile = {
          id: Math.random().toString(36).substr(2, 9),
          file,
          name: file.name,
          size: file.size,
          pageCount,
        };

        newFiles.push(pdfFile);
      } catch (error) {
        console.error(`Failed to read PDF file ${file.name}:`, error);
      }
    }

    setPdfFiles(prev => [...prev, ...newFiles]);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const removeFile = (id: string) => {
    setPdfFiles(prev => prev.filter(file => file.id !== id));
  };

  const moveFile = (id: string, direction: 'up' | 'down') => {
    setPdfFiles(prev => {
      const index = prev.findIndex(file => file.id === id);
      if (index === -1) return prev;
      
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= prev.length) return prev;
      
      const newFiles = [...prev];
      [newFiles[index], newFiles[newIndex]] = [newFiles[newIndex], newFiles[index]];
      return newFiles;
    });
  };

  const mergePDFs = async () => {
    if (pdfFiles.length < 2) {
      alert(t('error.minFiles'));
      return;
    }

    setIsProcessing(true);
    
    try {
      const mergedPdf = await PDFDocument.create();

      for (const pdfFile of pdfFiles) {
        const arrayBuffer = await pdfFile.file.arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach((page) => mergedPdf.addPage(page));
      }

      const pdfBytes = await mergedPdf.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      
      setMergedPdfUrl(url);
    } catch (error) {
      console.error('合并 PDF 失败:', error);
      alert(t('error.mergeFailed'));
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadMergedPDF = () => {
    if (!mergedPdfUrl) return;

    const link = document.createElement('a');
    link.href = mergedPdfUrl;
    link.download = 'merged.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const clearAll = () => {
    setPdfFiles([]);
    if (mergedPdfUrl) {
      URL.revokeObjectURL(mergedPdfUrl);
      setMergedPdfUrl(null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const totalPages = pdfFiles.reduce((sum, file) => sum + (file.pageCount || 0), 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>

          {/* Upload Area */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">{t('upload.title')}</h3>
                <p className="text-gray-500 mb-4">
                  {t('upload.description')}
                </p>
                <Button asChild>
                  <label>
                    {t('upload.button')}
                    <input
                      type="file"
                      multiple
                      accept=".pdf,application/pdf"
                      className="hidden"
                      onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                    />
                  </label>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* File List */}
          {pdfFiles.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileStack className="h-5 w-5" />
                      {t('fileList.title', { count: pdfFiles.length })}
                    </CardTitle>
                    <CardDescription>
                      {t('fileList.description', { totalPages })}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={mergePDFs}
                      disabled={pdfFiles.length < 2 || isProcessing}
                    >
                      {isProcessing ? t('fileList.merging') : t('fileList.mergeButton')}
                    </Button>
                    <Button onClick={clearAll} variant="outline">
                      {t('fileList.clearButton')}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {pdfFiles.map((file, index) => (
                    <div key={file.id} className="flex items-center gap-4 p-4 border rounded-lg bg-white">
                      <div className="flex flex-col gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => moveFile(file.id, 'up')}
                          disabled={index === 0}
                          className="h-6 w-6 p-0"
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => moveFile(file.id, 'down')}
                          disabled={index === pdfFiles.length - 1}
                          className="h-6 w-6 p-0"
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                      </div>
                      
                      <div className="flex-1">
                        <h4 className="font-medium">{file.name}</h4>
                        <div className="text-sm text-gray-500">
                          {formatFileSize(file.size)} • {t('fileList.pages', { count: file.pageCount || 0 })}
                        </div>
                      </div>

                      <div className="text-sm text-gray-500 min-w-[60px] text-center">
                        {t('fileList.position', { position: index + 1 })}
                      </div>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeFile(file.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Processing Status */}
          {isProcessing && (
            <Card className="mb-6">
              <CardContent className="p-6 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>{t('processing.message')}</p>
              </CardContent>
            </Card>
          )}

          {/* Download Result */}
          {mergedPdfUrl && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  {t('result.title')}
                </CardTitle>
                <CardDescription>
                  {t('result.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={downloadMergedPDF} className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  {t('result.downloadButton')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
