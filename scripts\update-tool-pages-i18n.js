const fs = require('fs');
const path = require('path');

// 获取所有工具页面目录
const toolsDir = path.join(__dirname, '../src/app/[locale]/tools');
const toolDirs = fs.readdirSync(toolsDir).filter(dir => {
  const fullPath = path.join(toolsDir, dir);
  return fs.statSync(fullPath).isDirectory();
});

console.log('Found tool directories:', toolDirs);

// 更新每个工具页面
toolDirs.forEach(toolDir => {
  const pageFile = path.join(toolsDir, toolDir, 'page.tsx');
  
  if (!fs.existsSync(pageFile)) {
    console.log(`Skipping ${toolDir} - no page.tsx found`);
    return;
  }
  
  console.log(`Updating ${toolDir}/page.tsx...`);
  
  let content = fs.readFileSync(pageFile, 'utf8');
  
  // 添加翻译导入
  if (!content.includes('structuredDataT') && !content.includes('navT')) {
    content = content.replace(
      /const categoryT = await getTranslations\('navigation\.categories'\);/,
      `const categoryT = await getTranslations('navigation.categories');
  const structuredDataT = await getTranslations('structuredData');
  const navT = await getTranslations('navigation');`
    );
  }
  
  // 更新 generateSoftwareApplicationSchema 调用
  content = content.replace(
    /const softwareSchema = generateSoftwareApplicationSchema\(\s*tool,\s*locale,\s*toolName,\s*toolDescription\s*\);/,
    `const softwareSchema = await generateSoftwareApplicationSchema(
    tool, 
    locale, 
    toolName, 
    toolDescription,
    structuredDataT
  );`
  );
  
  // 更新面包屑中的硬编码"首页"
  content = content.replace(
    /{ name: '首页', url: `\/\$\{locale\}` }/,
    `{ name: navT('home'), url: \`/\${locale}\` }`
  );
  
  fs.writeFileSync(pageFile, content, 'utf8');
  console.log(`✅ Updated ${toolDir}/page.tsx`);
});

console.log('All tool pages updated!');
