# 结构化数据开发指南

## 📋 概述

本指南详细说明了 AnyTool 项目中结构化数据的实现方式、最佳实践和维护方法。结构化数据是 SEO 优化的重要组成部分，帮助搜索引擎更好地理解和展示我们的工具页面。

## 🏗️ 架构设计

### 核心组件

1. **StructuredData 组件** (`src/components/StructuredData.tsx`)
   - 负责将 JSON-LD 数据注入到页面 HTML 中
   - 使用标准 HTML `<script>` 标签而非 Next.js Script 组件
   - 支持单个或多个 Schema 对象

2. **结构化数据生成函数** (`src/lib/structured-data.ts`)
   - `generateSoftwareApplicationSchema()` - 生成工具应用 Schema
   - `generateBreadcrumbSchema()` - 生成面包屑导航 Schema
   - `generateWebSiteSchema()` - 生成网站 Schema
   - `generateOrganizationSchema()` - 生成组织 Schema

3. **工具页面集成** (`src/app/[locale]/tools/*/page.tsx`)
   - 在服务器组件中生成结构化数据
   - 将结构化数据传递给 StructuredData 组件
   - 保持客户端组件的纯净性

## 🔧 实现模式

### 工具页面标准模式

每个工具页面都应该遵循以下模式：

```typescript
// src/app/[locale]/tools/[tool-name]/page.tsx
import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import { getTranslations } from 'next-intl/server';
import { getToolById } from '@/config/tools';
import { StructuredData } from '@/components/StructuredData';
import { generateSoftwareApplicationSchema, generateBreadcrumbSchema } from '@/lib/structured-data';
import ToolNameClient from './ToolNameClient';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: 'toolName',
    path: '/tools/tool-name'
  });
}

export default async function ToolNamePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  const t = await getTranslations('tools.toolName');
  const categoryT = await getTranslations('navigation.categories');
  
  // 获取工具信息
  const tool = getToolById('tool-name')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);
  
  // 生成结构化数据
  const softwareSchema = generateSoftwareApplicationSchema(
    tool, 
    locale, 
    toolName, 
    toolDescription
  );
  
  const breadcrumbSchema = generateBreadcrumbSchema(
    [
      { name: '首页', url: `/${locale}` },
      { name: categoryName, url: `/${locale}#${tool.category}` },
      { name: toolName, url: `/${locale}${tool.path}` }
    ],
    locale
  );

  return (
    <>
      <StructuredData data={[softwareSchema, breadcrumbSchema]} />
      <ToolNameClient />
    </>
  );
}
```

### 关键原则

1. **服务器端生成**: 结构化数据必须在服务器组件中生成
2. **客户端分离**: 客户端组件不应包含结构化数据逻辑
3. **多语言支持**: 所有文本内容都通过 next-intl 国际化
4. **标准化面包屑**: 使用统一的面包屑结构

## 📊 Schema 类型

### 1. SoftwareApplication Schema

用于描述工具应用的详细信息：

```json
{
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "工具名称",
  "description": "工具描述",
  "url": "工具URL",
  "applicationCategory": "WebApplication",
  "operatingSystem": "Any",
  "isAccessibleForFree": true,
  "inLanguage": "zh-CN",
  "softwareVersion": "1.0",
  "requirements": "JavaScript enabled browser",
  "author": { "@type": "Organization", "name": "AnyTool" },
  "publisher": { "@type": "Organization", "name": "AnyTool" },
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock"
  },
  "featureList": ["免费使用", "无需注册", "浏览器内处理", "隐私安全"]
}
```

### 2. BreadcrumbList Schema

用于描述页面导航路径：

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "首页",
      "item": "https://anytool.app/zh"
    },
    {
      "@type": "ListItem", 
      "position": 2,
      "name": "分类名称",
      "item": "https://anytool.app/zh#category"
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": "工具名称",
      "item": "https://anytool.app/zh/tools/tool-name"
    }
  ]
}
```

### 3. WebSite Schema

用于描述整个网站信息（在根布局中定义）：

```json
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "AnyTool",
  "alternateName": "AnyTool - 免费在线工具集",
  "url": "https://anytool.app/zh",
  "description": "提供图片处理、文本处理、数据转换、PDF操作等多种实用在线工具，完全免费使用",
  "inLanguage": "zh-CN",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://anytool.app/zh?search={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  }
}
```

### 4. Organization Schema

用于描述组织信息（在根布局中定义）：

```json
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "AnyTool",
  "url": "https://anytool.app",
  "logo": {
    "@type": "ImageObject",
    "url": "https://anytool.app/logo.png",
    "width": 512,
    "height": 512
  },
  "description": "提供免费在线工具的专业平台",
  "foundingDate": "2024",
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "availableLanguage": ["Chinese", "English"]
  }
}
```

## 🛠️ 开发工具

### 批量添加脚本

使用 `scripts/add-structured-data-to-tools.js` 为新工具批量添加结构化数据：

```bash
# 添加结构化数据到所有工具
node scripts/add-structured-data-to-tools.js

# 恢复备份文件（如果需要）
node scripts/add-structured-data-to-tools.js restore

# 显示帮助信息
node scripts/add-structured-data-to-tools.js help
```

### 验证脚本

使用 `scripts/check-structured-data.js` 验证结构化数据：

```bash
# 验证指定页面的结构化数据
node scripts/check-structured-data.js

# 验证所有工具页面（需要安装 axios 和 cheerio）
node scripts/verify-all-tools-structured-data.js
```

## ✅ 验证方法

### 1. 本地验证

```javascript
// 在浏览器控制台运行
const scripts = document.querySelectorAll('script[type="application/ld+json"]');
console.log(`发现 ${scripts.length} 个结构化数据块`);
scripts.forEach((script, i) => {
  const data = JSON.parse(script.textContent);
  console.log(`Schema ${i+1}: ${data['@type']} - ${data.name || '未命名'}`);
});
```

### 2. 在线验证工具

- **Schema.org 验证器**: https://validator.schema.org/
- **Google Rich Results Test**: https://search.google.com/test/rich-results
- **Google Structured Data Testing Tool**: https://search.google.com/structured-data/testing-tool

### 3. 自动化验证

```bash
# 运行验证脚本
node scripts/check-structured-data.js

# 预期输出
✅ 发现 4 个结构化数据块
✅ SoftwareApplication Schema 存在
✅ SoftwareApplication 所有必需字段完整
✅ BreadcrumbList Schema 存在
✅ 包含 3 个面包屑项
```

## 🚫 常见错误和避免方法

### 1. 无效属性错误

❌ **错误示例**:
```json
{
  "browserRequirements": "Requires JavaScript. Requires HTML5.",
  "permissions": "browser"
}
```

✅ **正确做法**:
```json
{
  "requirements": "JavaScript enabled browser"
}
```

### 2. 客户端组件中使用结构化数据

❌ **错误示例**:
```typescript
// 在客户端组件中
'use client';
export default function ToolClient() {
  return (
    <>
      <StructuredData data={schema} /> {/* 不会正确渲染 */}
      <div>工具内容</div>
    </>
  );
}
```

✅ **正确做法**:
```typescript
// 在服务器组件中
export default async function ToolPage() {
  const schema = generateSchema();
  return (
    <>
      <StructuredData data={schema} />
      <ToolClient />
    </>
  );
}
```

### 3. Next.js Script 组件问题

❌ **错误示例**:
```typescript
import Script from 'next/script';

export function StructuredData({ data }) {
  return (
    <Script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}
```

✅ **正确做法**:
```typescript
export function StructuredData({ data }) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data, null, 2) }}
    />
  );
}
```

## 📈 SEO 效果监控

### 1. Google Search Console

- 监控 Rich Results 展示情况
- 检查结构化数据错误
- 跟踪搜索表现变化

### 2. 关键指标

- **Rich Results 展示率**: 目标 >80%
- **点击率提升**: 预期 20-40%
- **排名提升**: 预期 5-15 位
- **结构化数据覆盖率**: 目标 100%

### 3. 监控工具

- Google Search Console
- Google Analytics
- 第三方 SEO 工具（如 SEMrush、Ahrefs）

## 🔄 维护和更新

### 新工具添加流程

1. **创建工具页面**: 按照标准模式创建 `page.tsx`
2. **配置工具信息**: 在 `src/config/tools.ts` 中添加工具配置
3. **添加翻译**: 在国际化文件中添加工具名称和描述
4. **验证结构化数据**: 使用验证脚本检查
5. **测试 Rich Results**: 使用 Google 工具验证

### 批量更新流程

1. **修改生成函数**: 更新 `src/lib/structured-data.ts`
2. **运行批量脚本**: 使用批量添加脚本更新所有页面
3. **验证更改**: 运行验证脚本确保正确性
4. **测试部署**: 在开发环境测试后部署

## 📚 参考资源

- [Schema.org 官方文档](https://schema.org/)
- [Google 结构化数据指南](https://developers.google.com/search/docs/appearance/structured-data)
- [Next.js 国际化文档](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [JSON-LD 规范](https://json-ld.org/)

## 🎯 最佳实践总结

1. **始终在服务器组件中生成结构化数据**
2. **使用标准 HTML script 标签而非 Next.js Script**
3. **确保所有文本内容都支持国际化**
4. **定期验证结构化数据的正确性**
5. **监控 SEO 效果并持续优化**
6. **保持 Schema.org 标准的最新性**
7. **为每个工具页面提供完整的结构化数据**
8. **使用自动化工具进行批量管理和验证**
