#!/usr/bin/env node

const https = require('https');
const http = require('http');

function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve(data);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

function extractStructuredData(html) {
  const regex = /<script[^>]*type="application\/ld\+json"[^>]*>(.*?)<\/script>/gs;
  const matches = [];
  let match;
  
  while ((match = regex.exec(html)) !== null) {
    try {
      const jsonData = JSON.parse(match[1]);
      matches.push(jsonData);
    } catch (e) {
      console.error('JSON解析错误:', e.message);
      console.error('原始内容:', match[1]);
    }
  }
  
  return matches;
}

async function checkStructuredData(url) {
  try {
    console.log(`🔍 检查页面: ${url}`);
    console.log('='.repeat(60));
    
    const html = await fetchPage(url);
    const structuredData = extractStructuredData(html);
    
    if (structuredData.length === 0) {
      console.log('❌ 未发现任何结构化数据');
      
      // 检查是否有ToolPageLayout组件
      if (html.includes('ToolPageLayout')) {
        console.log('⚠️  页面包含ToolPageLayout组件，但没有结构化数据');
      }
      
      // 检查是否有StructuredData组件
      if (html.includes('structured-data')) {
        console.log('⚠️  页面可能包含StructuredData组件');
      }
      
      return;
    }
    
    console.log(`✅ 发现 ${structuredData.length} 个结构化数据块\n`);
    
    structuredData.forEach((data, index) => {
      console.log(`📊 结构化数据 ${index + 1}:`);
      console.log(`类型: ${data['@type'] || '未知'}`);
      console.log(`名称: ${data.name || '未设置'}`);
      console.log('完整数据:');
      console.log(JSON.stringify(data, null, 2));
      console.log('-'.repeat(40));
    });
    
    // 验证必要字段
    const softwareApp = structuredData.find(d => d['@type'] === 'SoftwareApplication');
    const breadcrumbs = structuredData.find(d => d['@type'] === 'BreadcrumbList');
    
    console.log('\n🔍 验证结果:');
    if (softwareApp) {
      console.log('✅ SoftwareApplication Schema 存在');
      const requiredFields = ['name', 'description', 'url', 'applicationCategory'];
      const missingFields = requiredFields.filter(field => !softwareApp[field]);
      
      if (missingFields.length === 0) {
        console.log('✅ SoftwareApplication 所有必需字段完整');
      } else {
        console.log(`❌ SoftwareApplication 缺少字段: ${missingFields.join(', ')}`);
      }
    } else {
      console.log('❌ 缺少 SoftwareApplication Schema');
    }
    
    if (breadcrumbs) {
      console.log('✅ BreadcrumbList Schema 存在');
      if (breadcrumbs.itemListElement && breadcrumbs.itemListElement.length > 0) {
        console.log(`✅ 包含 ${breadcrumbs.itemListElement.length} 个面包屑项`);
      } else {
        console.log('❌ BreadcrumbList 缺少导航项');
      }
    } else {
      console.log('❌ 缺少 BreadcrumbList Schema');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

// 检查本地开发服务器
async function main() {
  const baseUrl = 'http://localhost:3002';
  const pages = [
    '/zh/tools/qr-generator',
    '/zh',
  ];
  
  for (const page of pages) {
    await checkStructuredData(`${baseUrl}${page}`);
    console.log('\n' + '='.repeat(80) + '\n');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
