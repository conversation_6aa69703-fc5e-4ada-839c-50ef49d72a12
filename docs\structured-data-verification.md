# 结构化数据验证指南

## 🔍 验证工具列表

### 1. Google 官方工具

#### Rich Results Test (推荐)
- **URL**: https://search.google.com/test/rich-results
- **用途**: 检测页面是否符合Google丰富摘要要求
- **优势**: Google官方工具，最权威

#### Schema Markup Validator
- **URL**: https://validator.schema.org/
- **用途**: 验证Schema.org标准合规性
- **优势**: 详细的错误报告

### 2. 第三方工具

#### Structured Data Linter
- **URL**: http://linter.structured-data.org/
- **用途**: 快速验证JSON-LD格式
- **优势**: 简单易用

#### SEO Site Checkup
- **URL**: https://seositecheckup.com/tools/structured-data-test
- **用途**: 综合SEO检查包含结构化数据
- **优势**: 一站式SEO检查

## 🛠️ 验证步骤

### 步骤1: 本地验证
```bash
# 启动开发服务器
npm run dev

# 运行验证脚本
node scripts/verify-structured-data.js
```

### 步骤2: 浏览器验证
1. 打开页面 (如: http://localhost:3000/zh/tools/qr-generator)
2. 按F12打开开发者工具
3. 在控制台运行:
```javascript
// 查看所有结构化数据
const scripts = document.querySelectorAll('script[type="application/ld+json"]');
console.log('发现', scripts.length, '个结构化数据块');
scripts.forEach((script, i) => {
  console.log(`数据块 ${i+1}:`, JSON.parse(script.textContent));
});
```

### 步骤3: 在线工具验证
1. 访问 Google Rich Results Test
2. 输入页面URL
3. 查看验证结果

## ✅ 验证清单

### SoftwareApplication Schema
- [ ] `@context`: "https://schema.org"
- [ ] `@type`: "SoftwareApplication"  
- [ ] `name`: 工具名称
- [ ] `description`: 工具描述
- [ ] `url`: 工具页面URL
- [ ] `applicationCategory`: "WebApplication"
- [ ] `isAccessibleForFree`: true
- [ ] `operatingSystem`: "Any"

### BreadcrumbList Schema
- [ ] `@context`: "https://schema.org"
- [ ] `@type`: "BreadcrumbList"
- [ ] `itemListElement`: 导航项数组
- [ ] 每个导航项包含: `@type`, `position`, `name`, `item`

### WebSite Schema (首页)
- [ ] `@context`: "https://schema.org"
- [ ] `@type`: "WebSite"
- [ ] `name`: 网站名称
- [ ] `url`: 网站URL
- [ ] `potentialAction`: 搜索功能

## 🚨 常见问题

### 问题1: JSON格式错误
**症状**: 控制台显示JSON解析错误
**解决**: 检查JSON语法，确保没有多余逗号

### 问题2: Schema类型不被识别
**症状**: Google工具显示"未知类型"
**解决**: 确保使用标准的Schema.org类型

### 问题3: 必需字段缺失
**症状**: 验证工具显示警告
**解决**: 添加Schema要求的必需字段

## 📊 验证结果示例

### 成功示例
```
✅ 发现的Schema类型: SoftwareApplication, BreadcrumbList
✅ SoftwareApplication 数据完整
✅ BreadcrumbList 包含 3 个导航项
🎉 所有必要的结构化数据都已正确实现！
```

### 错误示例
```
❌ SoftwareApplication 缺少字段: description, url
⚠️ 缺少以下结构化数据: BreadcrumbList
```

## 🔄 持续监控

### 定期检查
- 每次部署后验证主要页面
- 使用Google Search Console监控
- 定期运行自动化验证脚本

### 监控指标
- 结构化数据覆盖率
- Rich Results 展示率
- 搜索点击率变化
