#!/usr/bin/env node

/**
 * 测试 sitemap 生成是否正确
 */

// 模拟 Next.js 环境
process.env.NEXT_PUBLIC_BASE_URL = 'https://anytool.app';

// 导入配置
const { tools } = require('../src/config/tools.ts');
const { locales } = require('../src/i18n/config.ts');

// 模拟 sitemap 函数逻辑
function testSitemap() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  
  // 从配置文件获取所有已实现的工具
  const implementedTools = tools.filter(tool => tool.implemented !== false);
  
  console.log('🔍 Sitemap 生成测试');
  console.log('='.repeat(50));
  
  console.log(`📊 基础信息:`);
  console.log(`  基础URL: ${baseUrl}`);
  console.log(`  支持语言: ${locales.join(', ')}`);
  console.log(`  总工具数: ${tools.length}`);
  console.log(`  已实现工具数: ${implementedTools.length}`);
  
  console.log(`\n📋 已实现的工具列表:`);
  implementedTools.forEach((tool, index) => {
    console.log(`  ${index + 1}. ${tool.id} - ${tool.name}`);
    console.log(`     路径: ${tool.path}`);
    console.log(`     分类: ${tool.category}`);
  });
  
  console.log(`\n🌐 多语言 URL 示例:`);
  
  // 生成多语言 alternates 的辅助函数
  const generateAlternates = (path = '') => {
    const alternates = {};
    
    locales.forEach(locale => {
      const url = `${baseUrl}/${locale}${path}`;
      alternates[locale] = url;
      
      // 为中文添加额外的语言代码
      if (locale === 'zh') {
        alternates['zh-CN'] = url;
      }
      // 为英文添加额外的语言代码
      if (locale === 'en') {
        alternates['en-US'] = url;
      }
    });
    
    // 设置默认语言
    alternates['x-default'] = `${baseUrl}/zh${path}`;
    
    return alternates;
  };
  
  // 首页示例
  console.log(`\n  首页 alternates:`);
  const homeAlternates = generateAlternates();
  Object.entries(homeAlternates).forEach(([lang, url]) => {
    console.log(`    ${lang}: ${url}`);
  });
  
  // 工具页面示例（使用第一个工具）
  if (implementedTools.length > 0) {
    const firstTool = implementedTools[0];
    console.log(`\n  工具页面示例 (${firstTool.id}) alternates:`);
    const toolAlternates = generateAlternates(firstTool.path);
    Object.entries(toolAlternates).forEach(([lang, url]) => {
      console.log(`    ${lang}: ${url}`);
    });
  }
  
  // 计算总页面数
  const totalPages = locales.length * (1 + implementedTools.length); // 首页 + 工具页面
  console.log(`\n📈 Sitemap 统计:`);
  console.log(`  总页面数: ${totalPages}`);
  console.log(`  首页数: ${locales.length} (每种语言一个)`);
  console.log(`  工具页面数: ${locales.length * implementedTools.length} (${locales.length} 语言 × ${implementedTools.length} 工具)`);
  
  // 检查是否有未实现的工具
  const unimplementedTools = tools.filter(tool => tool.implemented === false);
  if (unimplementedTools.length > 0) {
    console.log(`\n⚠️  未实现的工具 (不会包含在 sitemap 中):`);
    unimplementedTools.forEach((tool, index) => {
      console.log(`  ${index + 1}. ${tool.id} - ${tool.name}`);
    });
  }
  
  console.log(`\n✅ Sitemap 测试完成！`);
  
  return {
    totalPages,
    implementedTools: implementedTools.length,
    unimplementedTools: unimplementedTools.length,
    languages: locales.length
  };
}

// 验证工具配置的完整性
function validateToolsConfig() {
  console.log('\n🔍 工具配置验证');
  console.log('='.repeat(50));
  
  const issues = [];
  
  tools.forEach((tool, index) => {
    // 检查必需字段
    const requiredFields = ['id', 'name', 'description', 'category', 'path', 'icon'];
    requiredFields.forEach(field => {
      if (!tool[field]) {
        issues.push(`工具 ${index + 1} (${tool.id || 'unknown'}) 缺少必需字段: ${field}`);
      }
    });
    
    // 检查路径格式
    if (tool.path && !tool.path.startsWith('/tools/')) {
      issues.push(`工具 ${tool.id} 的路径格式不正确: ${tool.path} (应该以 /tools/ 开头)`);
    }
    
    // 检查 ID 和路径的一致性
    if (tool.path && tool.id) {
      const expectedPath = `/tools/${tool.id}`;
      if (tool.path !== expectedPath) {
        issues.push(`工具 ${tool.id} 的路径与 ID 不一致: ${tool.path} (期望: ${expectedPath})`);
      }
    }
  });
  
  if (issues.length === 0) {
    console.log('✅ 所有工具配置都正确！');
  } else {
    console.log(`❌ 发现 ${issues.length} 个配置问题:`);
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }
  
  return issues;
}

// 主函数
function main() {
  console.log('🚀 AnyTool Sitemap 测试工具\n');
  
  try {
    // 验证工具配置
    const configIssues = validateToolsConfig();
    
    // 测试 sitemap 生成
    const stats = testSitemap();
    
    console.log('\n📊 总结报告:');
    console.log('='.repeat(50));
    console.log(`✅ 配置验证: ${configIssues.length === 0 ? '通过' : `${configIssues.length} 个问题`}`);
    console.log(`✅ 支持语言: ${stats.languages} 种`);
    console.log(`✅ 已实现工具: ${stats.implementedTools} 个`);
    console.log(`✅ 总页面数: ${stats.totalPages} 页`);
    
    if (stats.unimplementedTools > 0) {
      console.log(`⚠️  未实现工具: ${stats.unimplementedTools} 个`);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
