{"metadata": {"title": "AnyTool - 免费在线工具集合 | 图片处理、PDF工具、数据转换", "description": "AnyTool提供100+免费在线工具：图片压缩、PDF合并分割、二维码生成、密码生成器、URL编码、时间戳转换等，无需下载安装，浏览器直接使用，保护隐私安全", "keywords": "在线工具,免费工具,图片压缩,PDF工具,二维码生成器,密码生成器,URL编码,时间戳转换,数据转换,文本处理,哈希计算,UUID生成器,单位转换器,JSON格式化"}, "navigation": {"title": "AnyTool", "home": "首页", "search": "搜索工具...", "categories": {"image": "图片", "text": "文本", "data": "数据", "utility": "实用", "pdf": "PDF"}, "languageSwitch": "切换语言", "tools": {"image-compressor": "图片压缩器", "image-converter": "图片格式转换", "image-cropper": "图片裁切工具", "image-resizer": "图片尺寸调整", "image-watermark": "图片水印添加", "image-joiner": "图片拼接器", "text-diff": "文本差异对比", "markdown-converter": "Markdown 转换器", "text-analyzer": "文本统计分析", "text-formatter": "文本格式化", "regex-tester": "正则表达式测试", "text-crypto": "文本加密解密", "qr-generator": "二维码生成器", "json-formatter": "JSON 格式化", "csv-converter": "CSV 转换器", "url-encoder": "URL 编码解码", "timestamp-converter": "时间戳转换", "color-converter": "颜色转换器", "uuid-generator": "UUID 生成器", "password-generator": "密码生成器", "hash-calculator": "哈希计算器", "unit-converter": "单位转换器", "base-converter": "进制转换器", "random-generator": "随机数生成", "pdf-merger": "PDF 合并器", "pdf-splitter": "PDF 分割器", "pdf-to-image": "PDF 转图片", "fba-label-stamper": "FBA 亚马逊 FBA 标签添加 Made in China 工具"}}, "components": {"dragDropUpload": {"clickOrDrag": "点击选择或拖拽 PDF 文件", "supportedFormats": "支持格式: PDF (最大 {maxSize}MB)", "selectFile": "选择文件", "size": "大小", "uploaded": "已上传", "uploadFailed": "上传失败"}, "fbaSettingsPanel": {"textSettings": "文本设置", "advancedOptions": "& 高级选项", "addText": "添加文本", "textDescription": "将在检测到的\"新品\"或\"New\"位置附近添加此文本", "advancedOptionsLabel": "高级选项", "fontSize": "字体大小", "fontSizeRange": "px (6-20)", "positionAdjustment": "位置调整", "horizontalOffset": "水平偏移", "verticalOffset": "垂直偏移", "positionDescription": "微调文本相对于检测位置的偏移量", "debugMode": "启用调试模式", "debugModeDescription": "显示文本检测区域，帮助调试位置问题"}, "fbaActionButtons": {"processing": "处理中...", "processFile": "处理文件", "downloaded": "已下载", "downloadResult": "下载结果", "reset": "重置", "processHint": "💡 点击\"处理文件\"开始为 PDF 添加 Made in China 标签", "processCompleted": "✅ 处理完成！"}}, "home": {"hero": {"title": "强大的在线工具集合", "subtitle": "提供图片处理、文本转换、数据操作等多种实用工具", "description": "无需下载安装，在浏览器中直接使用。所有工具完全免费，保护您的隐私安全。", "cta": "开始使用"}, "features": {"title": "为什么选择 AnyTool？", "fast": {"title": "快速高效", "description": "所有工具都在浏览器中运行，无需等待上传下载"}, "free": {"title": "完全免费", "description": "所有功能永久免费使用，无隐藏费用"}, "easy": {"title": "易于使用", "description": "简洁直观的界面设计，无需学习即可上手"}}, "categories": {"title": "工具分类", "viewAll": "查看全部"}, "popular": {"title": "热门工具"}}, "tools": {"imageCompressor": {"name": "图片压缩器", "description": "压缩 JPG、PNG 图片，减小文件大小", "keywords": "图片压缩, 照片压缩, 减小文件大小, JPG压缩, PNG压缩, WebP转换, 图片优化, 照片调整, 图片质量, 文件大小减少, 批量压缩, 在线图片工具, 照片优化, 网页优化", "title": "图片压缩器", "subtitle": "压缩图片文件大小，保持良好的视觉质量", "upload": {"title": "拖拽图片到这里或点击选择", "subtitle": "支持 JPG、PNG、WebP 等格式，可同时选择多个文件", "button": "选择图片"}, "settings": {"title": "压缩设置", "description": "调整这些参数来控制压缩效果", "quality": "图片质量", "maxWidth": "最大宽度 (px)", "maxHeight": "最大高度 (px)", "format": "输出格式", "compress": "开始压缩"}, "processing": {"title": "正在压缩图片..."}, "result": {"title": "压缩结果", "images": "张图片", "downloadAll": "下载全部", "clear": "清空", "original": "原始文件", "compressed": "压缩后", "reduction": "减少了", "download": "下载"}, "info": {"title": "使用说明", "items": ["支持 JPG、PNG、WebP 格式的图片压缩", "可以调节压缩质量，平衡文件大小和图片质量", "支持设置最大宽度和高度，自动等比缩放", "所有处理都在浏览器中进行，不会上传到服务器", "压缩后可以直接下载，保护您的隐私"]}}, "imageConverter": {"name": "图片格式转换", "description": "JPG/PNG/WebP/AVIF 互转", "keywords": "图片格式转换, 图片转换, JPG转PNG, PNG转JPG, WebP转换, AVIF转换, 图片格式互转, 图片类型转换, 批量转换, 在线转换, 图片格式化, 图片格式修改, 格式转换工具, 图片处理", "title": "图片格式转换", "subtitle": "支持 JPG、PNG、WebP、AVIF 等格式互转，保持图片质量", "settings": {"title": "转换设置", "description": "选择目标格式和质量设置", "format": "目标格式", "quality": "图片质量"}, "upload": {"title": "选择图片文件", "description": "支持多种格式，可同时选择多个文件", "dragText": "拖拽图片到这里或点击选择", "selectFiles": "选择图片文件", "supportedFormats": "支持 JPG、PNG、WebP、BMP、GIF 格式", "processing": "转换中..."}, "results": {"title": "转换结果", "downloadAll": "下载全部", "clearAll": "清空全部", "download": "下载"}, "info": {"title": "使用说明", "items": ["支持 JPG、PNG、WebP、BMP、GIF 格式的图片转换", "可以调节 JPEG 格式的质量，平衡文件大小和图片质量", "支持批量转换，提高工作效率", "所有处理都在浏览器中进行，不会上传到服务器", "转换后可以直接下载，保护您的隐私"]}}, "jsonFormatter": {"name": "JSON 格式化器", "description": "格式化、压缩和验证 JSON 数据", "keywords": "JSON格式化, JSON美化, JSON压缩, JSON验证, JSON解析, JSON格式化工具, JSON语法检查, JSON编辑器, API响应格式化, JSON检查, 数据格式化, JSON查看器, JSON转换器", "title": "JSON 格式化器", "subtitle": "美化、压缩、验证 JSON 数据，支持语法高亮和错误检测", "actions": {"title": "操作面板", "description": "选择操作类型和设置", "format": "格式化", "minify": "压缩", "validate": "验证", "loadSample": "加载示例", "clear": "清空", "indentSize": "缩进大小", "spaces": "空格", "upload": "上传文件", "copy": "复制", "copied": "已复制", "download": "下载"}, "input": {"title": "输入 JSON", "description": "粘贴或输入需要处理的 JSON 数据", "placeholder": "在这里输入 JSON 数据..."}, "result": {"title": "输出结果", "description": "格式化后的 JSON 数据", "placeholder": "格式化结果将显示在这里...", "error": "JSON 错误", "valid": "JSON 格式正确", "invalid": "JSON 格式错误"}, "info": {"title": "使用说明", "items": ["<strong>格式化:</strong> 将压缩的 JSON 转换为易读的格式", "<strong>压缩:</strong> 移除所有不必要的空格和换行符", "<strong>验证:</strong> 检查 JSON 语法是否正确", "支持上传 .json 和 .txt 文件", "可以调整缩进大小来控制格式化样式", "所有处理都在浏览器中进行，保护数据隐私"]}}, "qrGenerator": {"name": "二维码生成器", "description": "免费在线二维码生成器，支持文本、网址、WiFi、电话、邮箱等多种类型，可自定义颜色和尺寸", "keywords": "二维码生成器,QR码生成,免费二维码,在线二维码,WiFi二维码,网址二维码,文本二维码", "title": "二维码生成器", "subtitle": "生成网址、文本、WiFi等各种类型的二维码", "content": {"title": "输入内容", "description": "输入要生成二维码的文本或链接", "placeholder": "输入文本、链接、WiFi 信息等...", "quickTemplates": "快速模板", "type": "内容类型", "types": {"text": "纯文本", "url": "网站链接", "email": "电子邮件", "phone": "电话号码", "sms": "短信", "wifi": "WiFi 连接"}, "placeholders": {"text": "输入要生成二维码的文本...", "url": "https://example.com", "email": "<EMAIL>", "phone": "+86 138 0013 8000", "sms": "输入短信内容...", "wifi": {"ssid": "WiFi 网络名称", "password": "WiFi 密码", "security": "加密类型"}}}, "settings": {"title": "自定义设置", "description": "调整二维码的外观和属性", "size": "尺寸", "errorCorrection": "错误纠正级别", "errorLevels": {"low": "低 (~7%)", "lowDesc": "适合干净环境", "medium": "中 (~15%)", "mediumDesc": "推荐使用", "quartile": "四分位 (~25%)", "quartileDesc": "适合一般环境", "high": "高 (~30%)", "highDesc": "适合恶劣环境"}, "foregroundColor": "前景色", "backgroundColor": "背景色", "generate": "生成二维码"}, "result": {"title": "二维码预览", "description": "生成的二维码预览", "downloadPng": "PNG", "downloadSvg": "SVG", "pixels": "像素", "placeholder": "输入内容生成二维码"}, "info": {"title": "使用说明", "items": ["支持文本、链接、WiFi、邮件、电话等多种格式", "可自定义二维码尺寸、颜色和错误纠正级别", "支持下载 PNG 和 SVG 格式", "错误纠正级别越高，二维码越复杂但容错性越强", "WiFi 格式: WIFI:T:WPA;S:网络名;P:密码;;", "所有处理都在浏览器中进行，保护隐私安全"]}}, "pdfMerger": {"name": "PDF 合并器", "description": "将多个 PDF 文件合并为一个", "keywords": "PDF合并, 合并PDF, PDF合并器, PDF组合, 文件合并, PDF连接, PDF拼接, PDF工具, 文档合并, 批量PDF处理, PDF编辑器", "title": "PDF 合并器", "subtitle": "将多个 PDF 文件合并为一个文件，支持拖拽排序和预览", "upload": {"title": "拖拽 PDF 文件到这里或点击选择", "description": "支持多个 PDF 文件，将按照列表顺序进行合并", "button": "选择 PDF 文件"}, "fileList": {"title": "PDF 文件列表 ({count} 个文件)", "description": "总共 {totalPages} 页 • 拖拽调整合并顺序", "mergeButton": "合并 PDF", "clearButton": "清空", "merging": "合并中...", "pages": "{count} 页", "position": "第 {position} 个"}, "processing": {"message": "正在合并 PDF 文件..."}, "result": {"title": "合并完成", "description": "PDF 文件已成功合并，点击下载", "downloadButton": "下载合并后的 PDF"}, "info": {"title": "使用说明", "items": ["支持同时选择多个 PDF 文件进行合并", "使用上下箭头调整文件合并顺序", "合并后的文件将包含所有原文件的页面", "所有处理都在浏览器中进行，不会上传到服务器", "支持拖拽上传，操作更便捷"]}}, "pdfSplitter": {"name": "PDF 分割器", "description": "将 PDF 按页数分割成多个文件", "keywords": "PDF分割, 分割PDF, PDF拆分, PDF分离器, 提取PDF页面, PDF页面提取器, PDF工具, 文档分割, 页面范围提取, PDF编辑器", "title": "PDF 分割器", "subtitle": "将 PDF 文件按页数或自定义范围分割成多个文件", "upload": {"title": "拖拽 PDF 文件到这里或点击选择", "subtitle": "选择要分割的 PDF 文件", "button": "选择 PDF 文件"}, "fileInfo": {"title": "文件信息", "filename": "文件名", "filesize": "文件大小", "totalPages": "总页数"}, "settings": {"title": "分割设置", "description": "选择分割方式和参数", "mode": "分割方式", "modes": {"pages": "按页数分割", "range": "自定义范围"}, "pagesPerFile": "每个文件包含页数", "willGenerate": "将生成 {count} 个文件", "pageRange": "页面范围 (例如: 1-3, 5, 7-10)", "pageRangePlaceholder": "1-3, 5, 7-10", "pageRangeDescription": "用逗号分隔多个范围，支持单页和页面范围", "split": "开始分割", "splitting": "分割中..."}, "processing": {"title": "正在分割 PDF 文件..."}, "result": {"title": "分割结果", "description": "点击下载单个文件或批量下载", "downloadAll": "下载全部", "clear": "清空", "download": "下载"}, "info": {"title": "使用说明", "items": ["按页数分割: 将 PDF 按指定页数平均分割", "自定义范围: 指定具体的页面范围进行分割", "范围格式: 1-3 表示第1到3页，5 表示第5页，用逗号分隔", "所有处理都在浏览器中进行，不会上传到服务器", "支持批量下载所有分割后的文件"]}}, "pdfToImage": {"name": "PDF 转图片", "description": "将 PDF 页面转换为 JPG/PNG 图片", "keywords": "PDF转图片, PDF转JPG, PDF转PNG, PDF转换, PDF转换器, 从PDF提取图片, PDF页面转图片, 文档转换器, PDF工具, 图片转换器, PDF提取器", "title": "PDF 转图片", "subtitle": "将 PDF 页面转换为 JPG 或 PNG 图片，支持自定义质量和尺寸", "upload": {"title": "拖拽 PDF 文件到这里或点击选择", "subtitle": "选择要转换的 PDF 文件", "button": "选择 PDF 文件", "loading": "正在加载 PDF 处理库..."}, "fileInfo": {"title": "文件信息", "filename": "文件名", "filesize": "文件大小", "totalPages": "总页数"}, "settings": {"title": "转换设置", "description": "配置输出图片的格式和质量", "format": "图片格式", "formats": {"png": "PNG (无损压缩)", "jpeg": "JPEG (有损压缩)"}, "scale": "缩放比例 ({scale}x)", "scaleDescription": "更高的缩放比例会产生更清晰但更大的图片", "quality": "JPEG 质量 ({quality}%)", "pageSelection": "页面选择", "pageSelectionPlaceholder": "all 或 1-3, 5, 7-10", "pageSelectionDescription": "输入 \"all\" 转换所有页面，或指定页面范围（如：1-3, 5, 7-10）", "convert": "开始转换", "converting": "转换中..."}, "processing": {"title": "正在转换 PDF 页面为图片..."}, "result": {"title": "转换结果", "description": "点击预览或下载图片", "downloadAll": "下载全部", "clear": "清空", "download": "下载", "page": "第 {page} 页"}, "info": {"title": "使用说明", "items": ["PNG 格式: 无损压缩，适合文档和图表", "JPEG 格式: 有损压缩，文件更小，适合照片", "缩放比例: 控制输出图片的分辨率和清晰度", "页面选择支持范围格式：1-3 表示第1到3页，用逗号分隔", "所有处理都在浏览器中进行，保护文件隐私"]}}, "fbaLabelStamper": {"name": "免费亚马逊 FBA 标签在线一键添加 Made in China", "description": "免费亚马逊 FBA 标签在线一键添加 Made in China", "keywords": "FBA标签, 亚马逊FBA, 中国制造, PDF加印, 标签修改, 亚马逊卖家工具, FBA合规, 产品标签, PDF编辑器, 亚马逊物流, FBA要求, 标签自动化, 中国产品, 原产地标签, 亚马逊市场", "title": "免费亚马逊 FBA 标签在线一键添加 Made in China", "subtitle": "为亚马逊 FBA 标签自动添加 \"Made in China\" 文本，智能识别\"新品\"或\"New\"位置", "upload": {"title": "上传 PDF 文件", "description": "选择需要添加文本的 FBA 标签 PDF 文件", "button": "选择 PDF 文件", "selected": "已选择: {filename} ({size} MB)"}, "textSettings": {"title": "文本设置", "description": "配置要添加的文本内容", "textToAdd": "添加的文本", "placeholder": "Made in China"}, "advancedSettings": {"title": "高级设置", "description": "调整文本位置和样式", "fontSize": "字体大小 (pt)", "xOffset": "水平偏移 (pt)", "yOffset": "垂直偏移 (pt)", "debugMode": "调试模式"}, "preview": {"title": "PDF 预览", "description": "PDF 文件预览", "original": "原始 PDF", "processed": "处理后的 PDF（已添加标记）", "noPreview": "未加载 PDF 文件进行预览", "loading": "加载预览中...", "error": "预览加载失败"}, "process": {"title": "开始处理", "description": "点击按钮开始处理 PDF 文件", "button": "处理 PDF", "processing": "处理中...", "reset": "重置"}, "download": {"title": "下载处理后的 PDF", "description": "下载已添加标记的 PDF 文件", "button": "下载处理后的 PDF", "notReady": "请先处理 PDF 文件以启用下载"}, "status": {"selectFile": "请先选择 PDF 文件", "libraryLoading": "库尚未加载完成，请稍候", "libraryLoaded": "库加载完成，可以开始处理文件", "libraryFailed": "库加载失败，请刷新页面重试", "analyzing": "正在分析 PDF 文本...", "processing": "正在添加文本...", "generating": "正在生成处理后的 PDF...", "completed": "处理完成！已处理 {count} 个标签，预览已更新为处理后的 PDF。", "failed": "处理失败: {error}", "previewUpdated": "预览已更新为处理后的 PDF"}, "info": {"title": "使用说明", "items": ["上传包含\"新品\"或\"New\"文字的 FBA 标签 PDF 文件", "工具会自动识别\"新品\"文字位置，如果没有找到则尝试识别\"New\"", "在目标文字后方添加指定文本（默认为\"Made in China\"）", "可调整文本大小、位置偏移等参数", "开启调试模式可查看识别的文字区域", "处理完成后可预览并下载修改后的 PDF 文件"]}}, "passwordGenerator": {"name": "密码生成器", "description": "免费在线强密码生成器，支持自定义长度、字符类型，生成安全可靠的随机密码", "keywords": "密码生成器,强密码生成,随机密码,安全密码,密码工具,在线密码生成器,复杂密码", "title": "密码生成器", "subtitle": "生成强密码和安全密码，支持自定义规则和选项", "settings": {"title": "密码设置", "description": "配置密码生成规则和选项", "length": "密码长度", "lengthDescription": "推荐：12-16个字符以获得强安全性", "includeUppercase": "包含大写字母（A-Z）", "includeLowercase": "包含小写字母（a-z）", "includeNumbers": "包含数字（0-9）", "includeSymbols": "包含符号（!@#$%^&*）", "excludeSimilar": "排除相似字符（0, O, l, I）", "excludeAmbiguous": "排除易混淆字符（{}, [], (), /\\, ~, ` 等）", "customCharacters": "自定义字符", "customCharactersPlaceholder": "输入要包含的自定义字符...", "generate": "生成密码", "generateMultiple": "生成多个（{count}个）"}, "result": {"title": "生成的密码", "multipleTitle": "生成的密码（{count}个）", "strength": "密码强度", "strengthLevels": {"weak": "弱", "fair": "一般", "good": "良好", "strong": "强", "veryStrong": "很强"}, "copy": "复制", "copied": "已复制！", "copyAll": "复制全部", "regenerate": "重新生成", "clear": "清空"}, "analysis": {"title": "密码分析", "length": "长度：{length}个字符", "entropy": "熵值：{entropy}位", "timeToCrack": "破解时间：{time}", "timeToCrackValues": {"instant": "瞬间", "seconds": "{count}秒", "minutes": "{count}分钟", "hours": "{count}小时", "days": "{count}天", "months": "{count}个月", "years": "{count}年", "centuries": "{count}个世纪"}}, "info": {"title": "安全提示", "items": ["<strong>长度很重要：</strong>更长的密码破解难度呈指数级增长", "<strong>使用多样性：</strong>包含大写、小写、数字和符号", "<strong>避免模式：</strong>不要使用字典词汇、个人信息或可预测的模式", "<strong>唯一密码：</strong>为不同账户使用不同的密码", "<strong>密码管理器：</strong>考虑使用密码管理器来存储复杂密码", "<strong>定期更新：</strong>定期更改密码，特别是重要账户"]}}, "urlEncoder": {"name": "URL编码解码器", "description": "免费在线URL编码解码工具，支持URL编码、URL解码、查询参数编码，处理特殊字符和中文字符", "keywords": "URL编码,URL解码,网址编码,查询参数编码,特殊字符编码,中文URL编码,在线编码工具", "title": "URL编码解码器", "subtitle": "对URL、查询参数和URL组件进行编码和解码，支持多种编码类型", "input": {"title": "输入文本", "description": "输入要编码/解码的文本或URL", "placeholder": "输入要编码/解码的URL或文本...", "loadSample": "加载示例", "clear": "清空"}, "actions": {"title": "操作", "description": "选择编码/解码操作", "encode": "编码", "decode": "解码", "encodeComponent": "组件编码", "decodeComponent": "组件解码", "encodeBase64": "Base64编码", "decodeBase64": "Base64解码"}, "result": {"title": "结果", "description": "编码/解码结果", "placeholder": "结果将在这里显示...", "copy": "复制", "copied": "已复制！", "download": "下载"}, "examples": {"title": "常见示例", "url": {"title": "完整URL", "original": "https://example.com/search?q=你好世界&lang=zh", "encoded": "https%3A//example.com/search%3Fq%3D%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C%26lang%3Dzh"}, "component": {"title": "URL组件", "original": "你好世界 & 特殊字符！", "encoded": "%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C%20%26%20%E7%89%B9%E6%AE%8A%E5%AD%97%E7%AC%A6%EF%BC%81"}, "query": {"title": "查询参数", "original": "用户名 包含空格", "encoded": "%E7%94%A8%E6%88%B7%E5%90%8D%20%E5%8C%85%E5%90%AB%E7%A9%BA%E6%A0%BC"}}, "info": {"title": "使用指南", "items": ["<strong>URL编码：</strong>将特殊字符转换为百分号编码格式，确保URL传输安全", "<strong>URL解码：</strong>将百分号编码字符转换回原始格式", "<strong>组件编码：</strong>仅编码URL组件部分（查询参数、路径段）", "<strong>Base64：</strong>用于二进制数据或特殊字符的替代编码方法", "<strong>常见用途：</strong>API参数、表单数据、搜索查询、URL中的文件名", "所有处理都在浏览器中完成，保护隐私安全"]}}, "uuidGenerator": {"name": "UUID 生成器", "description": "生成各种版本的 UUID", "keywords": "UUID生成器, GUID生成器, 唯一标识符, UUID v1, UUID v4, UUID v7, 全局唯一标识符, 随机ID生成器, 数据库ID, API密钥生成器, 唯一字符串, 标识符生成器", "title": "UUID 生成器", "subtitle": "生成各种版本的唯一标识符（UUID），支持批量生成和自定义格式", "generator": {"title": "UUID 生成器", "description": "选择 UUID 版本和生成数量", "version": "UUID 版本", "versions": {"v1": "版本 1 (基于时间戳和MAC地址)", "v4": "版本 4 (随机生成，推荐)", "v7": "版本 7 (基于时间戳，可排序)"}, "count": "生成数量", "countDescription": "一次生成多个 UUID", "format": "输出格式", "formats": {"standard": "标准格式 (带连字符)", "compact": "紧凑格式 (无连字符)", "uppercase": "大写格式", "braces": "大括号格式 {UUID}", "quotes": "引号格式 \"UUID\""}, "generate": "生成 UUID", "generateMultiple": "生成 {count} 个 UUID"}, "result": {"title": "生成的 UUID", "multipleTitle": "生成的 UUID ({count} 个)", "copy": "复制", "copied": "已复制！", "copyAll": "复制全部", "download": "下载", "clear": "清空", "regenerate": "重新生成"}, "examples": {"title": "UUID 示例", "v1": {"title": "UUID v1 (基于时间)", "description": "包含时间戳和节点信息，可以追溯生成时间", "example": "6ba7b810-9dad-11d1-80b4-00c04fd430c8"}, "v4": {"title": "UUID v4 (随机)", "description": "完全随机生成，最常用的版本", "example": "550e8400-e29b-41d4-a716-************"}, "v7": {"title": "UUID v7 (时间排序)", "description": "基于时间戳，支持按时间排序", "example": "01890a5d-ac96-774b-bcce-b302099a8057"}}, "validator": {"title": "UUID 验证器", "description": "验证 UUID 格式是否正确", "input": "输入 UUID", "placeholder": "输入要验证的 UUID...", "validate": "验证", "valid": "有效的 UUID", "invalid": "无效的 UUID", "details": {"version": "版本：{version}", "variant": "变体：{variant}", "timestamp": "时间戳：{timestamp}", "node": "节点：{node}"}}, "info": {"title": "UUID 说明", "items": ["<strong>UUID v1：</strong>基于时间戳和MAC地址，可以追溯生成时间和位置", "<strong>UUID v4：</strong>完全随机生成，最常用，安全性高", "<strong>UUID v7：</strong>基于时间戳，支持按时间排序，适合数据库主键", "<strong>唯一性：</strong>UUID 在全球范围内唯一，重复概率极低", "<strong>格式：</strong>标准格式为 8-4-4-4-12 的十六进制字符", "所有生成都在浏览器中完成，保护隐私安全"]}}, "timestampConverter": {"name": "时间戳转换器", "description": "在时间戳和人类可读日期之间转换", "keywords": "时间戳转换器, Unix时间戳, 时间戳转换, 日期转换, 时间转换, 时间戳转日期, 日期转时间戳, Unix时间, 毫秒转换器, ISO 8601, RFC 2822, 时区转换器", "title": "时间戳转换器", "subtitle": "在Unix时间戳和人类可读日期之间转换，支持时区设置", "current": {"title": "当前时间", "description": "当前时间戳和日期", "timestamp": "当前时间戳", "date": "当前日期", "timezone": "时区"}, "converter": {"title": "时间戳转换器", "description": "在时间戳和日期格式之间转换", "timestampToDate": "时间戳转日期", "dateToTimestamp": "日期转时间戳", "timestampInput": "输入时间戳", "timestampPlaceholder": "1640995200", "dateInput": "选择日期和时间", "convert": "转换", "result": "结果", "copy": "复制", "copied": "已复制！", "clear": "清空"}, "formats": {"title": "常见格式", "description": "不同格式的时间戳", "seconds": "秒（Unix）", "milliseconds": "毫秒（JavaScript）", "iso": "ISO 8601", "rfc": "RFC 2822", "relative": "相对时间"}, "batch": {"title": "批量转换", "description": "一次转换多个时间戳", "input": "输入时间戳（每行一个）", "placeholder": "1640995200\n1641081600\n1641168000", "convertAll": "全部转换", "results": "转换结果", "download": "下载CSV"}, "examples": {"title": "常见示例", "now": {"title": "当前时间", "timestamp": "当前时间戳", "date": "当前日期"}, "epoch": {"title": "Unix纪元", "timestamp": "0", "date": "1970年1月1日 00:00:00 UTC"}, "y2k": {"title": "千年虫", "timestamp": "946684800", "date": "2000年1月1日 00:00:00 UTC"}}, "info": {"title": "使用指南", "items": ["<strong>Unix时间戳：</strong>自1970年1月1日00:00:00 UTC以来的秒数", "<strong>JavaScript时间戳：</strong>自Unix纪元以来的毫秒数", "<strong>时区支持：</strong>转换到不同的时区", "<strong>批量处理：</strong>一次转换多个时间戳", "<strong>常见格式：</strong>ISO 8601、RFC 2822、相对时间", "所有处理都在浏览器中完成，保护隐私安全"]}}, "unitConverter": {"name": "单位转换器", "description": "转换长度、重量、温度等各种单位", "keywords": "单位转换器, 度量转换器, 长度转换器, 重量转换器, 温度转换器, 面积转换器, 体积转换器, 公制转换器, 英制转换器, 烹饪转换器, 距离转换器, 速度转换器", "title": "单位转换器", "subtitle": "在不同的测量单位之间进行转换，支持长度、重量、温度、面积、体积等", "categories": {"length": "长度", "weight": "重量", "temperature": "温度", "area": "面积", "volume": "体积", "time": "时间", "speed": "速度", "energy": "能量"}, "units": {"length": {"mm": "毫米 (mm)", "cm": "厘米 (cm)", "m": "米 (m)", "km": "千米 (km)", "in": "英寸 (in)", "ft": "英尺 (ft)", "yd": "码 (yd)", "mi": "英里 (mi)"}, "weight": {"mg": "毫克 (mg)", "g": "克 (g)", "kg": "千克 (kg)", "t": "吨 (t)", "oz": "盎司 (oz)", "lb": "磅 (lb)", "st": "英石 (st)"}, "temperature": {"c": "摄氏度 (°C)", "f": "华氏度 (°F)", "k": "开尔文 (K)", "r": "兰氏度 (°R)"}, "area": {"mm2": "平方毫米 (mm²)", "cm2": "平方厘米 (cm²)", "m2": "平方米 (m²)", "km2": "平方千米 (km²)", "in2": "平方英寸 (in²)", "ft2": "平方英尺 (ft²)", "yd2": "平方码 (yd²)", "mi2": "平方英里 (mi²)", "acre": "英亩 (acre)", "ha": "公顷 (ha)"}, "volume": {"ml": "毫升 (ml)", "l": "升 (l)", "m3": "立方米 (m³)", "in3": "立方英寸 (in³)", "ft3": "立方英尺 (ft³)", "gal": "加仑 (gal)", "qt": "夸脱 (qt)", "pt": "品脱 (pt)", "cup": "杯 (cup)", "fl_oz": "液体盎司 (fl oz)"}, "time": {"ms": "毫秒 (ms)", "s": "秒 (s)", "min": "分钟 (min)", "h": "小时 (h)", "d": "天 (d)", "w": "周 (w)", "mo": "月 (mo)", "y": "年 (y)"}, "speed": {"mps": "米/秒 (m/s)", "kph": "千米/小时 (km/h)", "mph": "英里/小时 (mph)", "fps": "英尺/秒 (ft/s)", "knot": "节 (knot)"}, "energy": {"j": "焦耳 (J)", "kj": "千焦 (kJ)", "cal": "卡路里 (cal)", "kcal": "千卡 (kcal)", "wh": "瓦时 (Wh)", "kwh": "千瓦时 (kWh)", "btu": "英热单位 (BTU)"}}, "converter": {"title": "单位转换", "description": "选择类别和单位进行转换", "category": "转换类别", "fromUnit": "从", "toUnit": "到", "fromValue": "输入值", "toValue": "转换结果", "fromPlaceholder": "输入要转换的数值...", "swap": "交换单位", "clear": "清空", "copy": "复制结果", "copied": "已复制！"}, "quickConvert": {"title": "快速转换", "description": "常用单位转换", "common": [{"name": "厘米转英寸", "category": "length", "from": "cm", "to": "in", "value": "30"}, {"name": "千克转磅", "category": "weight", "from": "kg", "to": "lb", "value": "70"}, {"name": "摄氏度转华氏度", "category": "temperature", "from": "c", "to": "f", "value": "25"}, {"name": "升转加仑", "category": "volume", "from": "l", "to": "gal", "value": "10"}]}, "examples": {"title": "常见用途", "cooking": {"title": "烹饪测量", "description": "转换食谱中的重量和体积单位"}, "travel": {"title": "旅行规划", "description": "转换距离、速度和温度单位"}, "construction": {"title": "建筑工程", "description": "转换长度、面积和体积单位"}, "science": {"title": "科学计算", "description": "转换各种物理量单位"}}, "info": {"title": "使用说明", "items": ["支持8大类别的单位转换：长度、重量、温度、面积、体积、时间、速度、能量", "提供常用单位的快速转换模板", "支持高精度计算，保留适当的小数位数", "温度转换支持摄氏度、华氏度、开尔文和兰氏度", "所有计算都在浏览器中完成，保护隐私安全", "支持复制转换结果和交换单位功能"]}}, "hashCalculator": {"name": "哈希计算器", "description": "计算文件或文本的哈希值", "keywords": "哈希计算器, MD5生成器, SHA1生成器, SHA256生成器, SHA512生成器, 校验和计算器, 文件完整性, 哈希验证, 加密哈希, 文件校验和, 数据完整性, 安全哈希", "title": "哈希计算器", "subtitle": "计算文件或文本的MD5、SHA1、SHA256等哈希值，用于文件校验和安全验证", "input": {"title": "输入数据", "description": "选择输入类型并提供数据", "type": "输入类型", "types": {"text": "文本输入", "file": "文件上传"}, "textPlaceholder": "输入要计算哈希值的文本...", "fileUpload": "拖拽文件到这里或点击选择", "fileButton": "选择文件", "loadSample": "加载示例", "clear": "清空"}, "algorithms": {"title": "哈希算法", "description": "选择要使用的哈希算法", "md5": "MD5 (128位)", "sha1": "SHA-1 (160位)", "sha256": "SHA-256 (256位)", "sha512": "SHA-512 (512位)", "selectAll": "全选", "deselectAll": "取消全选"}, "calculate": "计算哈希值", "calculating": "计算中...", "result": {"title": "哈希结果", "description": "计算得到的哈希值", "algorithm": "算法", "hash": "哈希值", "copy": "复制", "copied": "已复制！", "copyAll": "复制全部", "download": "下载结果", "clear": "清空结果"}, "fileInfo": {"title": "文件信息", "name": "文件名", "size": "文件大小", "type": "文件类型", "lastModified": "最后修改"}, "verification": {"title": "哈希验证", "description": "输入已知哈希值进行验证", "input": "输入已知哈希值", "placeholder": "输入要验证的哈希值...", "verify": "验证", "match": "✓ 哈希值匹配", "noMatch": "✗ 哈希值不匹配", "selectHash": "选择要验证的哈希算法"}, "examples": {"title": "常见用途", "fileIntegrity": {"title": "文件完整性验证", "description": "验证下载文件是否完整且未被篡改"}, "passwordSecurity": {"title": "密码安全存储", "description": "将密码转换为哈希值进行安全存储"}, "dataVerification": {"title": "数据一致性检查", "description": "确保数据在传输过程中未被修改"}}, "info": {"title": "算法说明", "items": ["<strong>MD5：</strong>快速但安全性较低，适合非安全场景的校验", "<strong>SHA-1：</strong>比MD5更安全，但已不推荐用于安全应用", "<strong>SHA-256：</strong>目前广泛使用的安全哈希算法，推荐使用", "<strong>SHA-512：</strong>更高安全性的哈希算法，适合高安全要求", "<strong>用途：</strong>文件校验、密码存储、数字签名、区块链等", "所有计算都在浏览器中完成，保护文件和数据隐私"]}}}, "common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "download": "下载", "upload": "上传", "clear": "清空", "copy": "复制", "paste": "粘贴", "save": "保存", "delete": "删除", "edit": "编辑", "view": "查看", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "finish": "完成", "retry": "重试", "fileSize": "文件大小", "fileName": "文件名", "format": "格式", "quality": "质量", "size": "尺寸", "color": "颜色", "settings": "设置", "preview": "预览", "result": "结果", "processing": "处理中", "completed": "已完成", "failed": "失败", "bytes": "字节", "kb": "KB", "mb": "MB", "gb": "GB"}}