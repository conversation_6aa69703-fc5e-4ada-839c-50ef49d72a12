"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { Upload, Download, Settings, Info } from "lucide-react";
import imageCompression from "browser-image-compression";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface CompressedImage {
  original: File;
  compressed: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  url: string;
}

export default function ImageCompressorClient() {
  const t = useTranslations('tools.imageCompressor');
  const [images, setImages] = useState<CompressedImage[]>([]);
  const [isCompressing, setIsCompressing] = useState(false);
  const [quality, setQuality] = useState(0.8);
  const [maxWidth, setMaxWidth] = useState(1920);
  const [maxHeight, setMaxHeight] = useState(1080);

  const handleFileSelect = useCallback(async (files: FileList) => {
    setIsCompressing(true);
    const newImages: CompressedImage[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!file.type.startsWith('image/')) {
        continue;
      }

      try {
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: Math.max(maxWidth, maxHeight),
          useWebWorker: true,
          quality: quality,
        };

        const compressedFile = await imageCompression(file, options);
        const compressionRatio = ((file.size - compressedFile.size) / file.size * 100);
        
        const compressedImage: CompressedImage = {
          original: file,
          compressed: compressedFile,
          originalSize: file.size,
          compressedSize: compressedFile.size,
          compressionRatio: compressionRatio,
          url: URL.createObjectURL(compressedFile),
        };

        newImages.push(compressedImage);
      } catch (error) {
        console.error('Compression failed:', error);
      }
    }

    setImages(prev => [...prev, ...newImages]);
    setIsCompressing(false);
  }, [quality, maxWidth, maxHeight]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const downloadImage = (image: CompressedImage) => {
    const link = document.createElement('a');
    link.href = image.url;
    link.download = `compressed_${image.original.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadAll = () => {
    images.forEach(image => {
      setTimeout(() => downloadImage(image), 100);
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const clearAll = () => {
    images.forEach(image => URL.revokeObjectURL(image.url));
    setImages([]);
  };

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('subtitle')}
            </p>
          </div>

          {/* Settings */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                {t('settings.title')}
              </CardTitle>
              <CardDescription>
                {t('settings.description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="grid md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  {t('settings.quality')} ({Math.round(quality * 100)}%)
                </label>
                <Input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={quality}
                  onChange={(e) => setQuality(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('settings.maxWidth')}</label>
                <Input
                  type="number"
                  value={maxWidth}
                  onChange={(e) => setMaxWidth(parseInt(e.target.value) || 1920)}
                  placeholder="1920"
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('settings.maxHeight')}</label>
                <Input
                  type="number"
                  value={maxHeight}
                  onChange={(e) => setMaxHeight(parseInt(e.target.value) || 1080)}
                  placeholder="1080"
                />
              </div>
            </CardContent>
          </Card>

          {/* Upload Area */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">{t('upload.title')}</h3>
                <p className="text-muted-foreground mb-4">
                  {t('upload.subtitle')}
                </p>
                <Button asChild>
                  <label>
                    {t('upload.button')}
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                    />
                  </label>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Processing Status */}
          {isCompressing && (
            <Card className="mb-6">
              <CardContent className="p-6 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>{t('processing.title')}</p>
              </CardContent>
            </Card>
          )}

          {/* Results */}
          {images.length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    {t('result.title')} ({images.length} {t('result.images')})
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button onClick={downloadAll} size="sm">
                      {t('result.downloadAll')}
                    </Button>
                    <Button onClick={clearAll} variant="outline" size="sm">
                      {t('result.clear')}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {images.map((image, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                      <img
                        src={image.url}
                        alt={image.original.name}
                        className="w-16 h-16 object-cover rounded"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{image.original.name}</h4>
                        <div className="text-sm text-muted-foreground">
                          <span>{formatFileSize(image.originalSize)}</span>
                          <span className="mx-2">→</span>
                          <span>{formatFileSize(image.compressedSize)}</span>
                          <span className="ml-2 text-green-600">
                            (-{image.compressionRatio.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                      <Button onClick={() => downloadImage(image)} size="sm">
                        {t('result.download')}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Info */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
