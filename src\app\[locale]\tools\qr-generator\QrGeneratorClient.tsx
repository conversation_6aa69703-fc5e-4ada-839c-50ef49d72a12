"use client";

import { useState, useEffect, useRef } from "react";
import { Download, QrC<PERSON>, Settings, Info } from "lucide-react";
import QRCode from "qrcode";
import { useTranslations, useLocale } from "next-intl";
import { TopNavigation } from "@/components/TopNavigation";
import { Breadcrumb, generateToolBreadcrumbs } from '@/components/Breadcrumb';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { getToolById } from "@/config/tools";

export default function QrGeneratorClient() {
  const t = useTranslations('tools.qrGenerator');
  const categoryT = useTranslations('navigation.categories');
  const locale = useLocale();

  // 获取工具信息
  const tool = getToolById('qr-generator')!;
  const toolName = t('name');
  const toolDescription = t('description');
  const categoryName = categoryT(tool.category as any);

  // 生成面包屑
  const breadcrumbs = generateToolBreadcrumbs(toolName, categoryName, locale);
  const [text, setText] = useState("");
  const [qrCodeUrl, setQrCodeUrl] = useState("");
  const [size, setSize] = useState(256);
  const [errorLevel, setErrorLevel] = useState<"L" | "M" | "Q" | "H">("M");
  const [foregroundColor, setForegroundColor] = useState("#000000");
  const [backgroundColor, setBackgroundColor] = useState("#ffffff");
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const generateQRCode = async () => {
    if (!text.trim()) {
      setQrCodeUrl("");
      return;
    }

    try {
      const options = {
        width: size,
        errorCorrectionLevel: errorLevel,
        color: {
          dark: foregroundColor,
          light: backgroundColor,
        },
      };

      const url = await QRCode.toDataURL(text, options);
      setQrCodeUrl(url);

      // 同时生成到 canvas 用于下载
      if (canvasRef.current) {
        await QRCode.toCanvas(canvasRef.current, text, options);
      }
    } catch (error) {
      console.error("Generate QR code failed:", error);
    }
  };

  useEffect(() => {
    generateQRCode();
  }, [text, size, errorLevel, foregroundColor, backgroundColor]);

  const downloadQRCode = () => {
    if (!canvasRef.current) return;

    const link = document.createElement("a");
    link.download = "qrcode.png";
    link.href = canvasRef.current.toDataURL();
    link.click();
  };

  const downloadSVG = async () => {
    if (!text.trim()) return;

    try {
      const options = {
        width: size,
        errorCorrectionLevel: errorLevel,
        color: {
          dark: foregroundColor,
          light: backgroundColor,
        },
      };

      const svgString = await QRCode.toString(text, { ...options, type: "svg" });
      const blob = new Blob([svgString], { type: "image/svg+xml" });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement("a");
      link.download = "qrcode.svg";
      link.href = url;
      link.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download SVG failed:", error);
    }
  };

  const presetTexts = [
    { label: t('content.types.url'), value: "https://www.example.com" },
    { label: t('content.types.wifi'), value: "WIFI:T:WPA;S:MyNetwork;P:MyPassword;;" },
    { label: t('content.types.email'), value: "mailto:<EMAIL>" },
    { label: t('content.types.phone'), value: "tel:+86-138-0000-0000" },
    { label: t('content.types.sms'), value: "sms:+86-138-0000-0000?body=Hello" },
  ];

  const errorLevels = [
    { value: "L", label: t('settings.errorLevels.low'), description: t('settings.errorLevels.lowDesc') },
    { value: "M", label: t('settings.errorLevels.medium'), description: t('settings.errorLevels.mediumDesc') },
    { value: "Q", label: t('settings.errorLevels.quartile'), description: t('settings.errorLevels.quartileDesc') },
    { value: "H", label: t('settings.errorLevels.high'), description: t('settings.errorLevels.highDesc') },
  ];

  return (
    <>
      <TopNavigation />

      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-6">
          {/* 面包屑导航 */}
          <Breadcrumb
            items={breadcrumbs}
            className="mb-6"
          />

          {/* 页面标题区域 */}
          <div className="mb-8">
            <h1 className="text-3xl text-center font-bold text-gray-900 mb-2">
              {toolName}
            </h1>
            <p className="text-center text-lg text-gray-600">
              {toolDescription}
            </p>
          </div>

          {/* 工具内容 */}
          <div className="max-w-4xl mx-auto">

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Input Section */}
            <div className="space-y-6">
              {/* Text Input */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('content.title')}</CardTitle>
                  <CardDescription>{t('content.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    placeholder={t('content.placeholder')}
                    className="min-h-[120px]"
                  />

                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">{t('content.quickTemplates')}:</p>
                    <div className="flex flex-wrap gap-2">
                      {presetTexts.map((preset, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => setText(preset.value)}
                        >
                          {preset.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    {t('settings.title')}
                  </CardTitle>
                  <CardDescription>{t('settings.description')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      {t('settings.size')}: {size}px
                    </label>
                    <Input
                      type="range"
                      min="128"
                      max="512"
                      step="32"
                      value={size}
                      onChange={(e) => setSize(parseInt(e.target.value))}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">{t('settings.errorCorrection')}</label>
                    <select
                      value={errorLevel}
                      onChange={(e) => setErrorLevel(e.target.value as "L" | "M" | "Q" | "H")}
                      className="w-full px-3 py-2 border rounded-md text-sm"
                    >
                      {errorLevels.map((level) => (
                        <option key={level.value} value={level.value}>
                          {level.label} - {level.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">{t('settings.foregroundColor')}</label>
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={foregroundColor}
                          onChange={(e) => setForegroundColor(e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          type="text"
                          value={foregroundColor}
                          onChange={(e) => setForegroundColor(e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">{t('settings.backgroundColor')}</label>
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          type="text"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Output Section */}
            <div className="space-y-6">
              {/* QR Code Preview */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <QrCode className="h-5 w-5" />
                        {t('result.title')}
                      </CardTitle>
                      <CardDescription>{t('result.description')}</CardDescription>
                    </div>
                    {qrCodeUrl && (
                      <div className="flex gap-2">
                        <Button onClick={downloadQRCode} size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          {t('result.downloadPng')}
                        </Button>
                        <Button onClick={downloadSVG} size="sm" variant="outline">
                          <Download className="h-4 w-4 mr-2" />
                          {t('result.downloadSvg')}
                        </Button>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-center">
                    {qrCodeUrl ? (
                      <div className="text-center">
                        <img
                          src={qrCodeUrl}
                          alt="Generated QR Code"
                          className="border rounded-lg shadow-sm"
                          style={{ width: size, height: size }}
                        />
                        <p className="text-sm text-muted-foreground mt-2">
                          {size} × {size} {t('result.pixels')}
                        </p>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                        <div className="text-center">
                          <QrCode className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-muted-foreground">{t('result.placeholder')}</p>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Hidden canvas for download */}
                  <canvas ref={canvasRef} style={{ display: "none" }} />
                </CardContent>
              </Card>

              {/* Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="h-5 w-5" />
                    {t('info.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    {t.raw('info.items').map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
          </div>
        </div>
      </div>
    </>
  );
}