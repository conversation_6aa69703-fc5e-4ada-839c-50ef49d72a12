#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要转换的工具页面配置
const toolConfigs = [
  { name: 'pdf-splitter', toolName: 'pdfSplitter' },
  { name: 'pdf-to-image', toolName: 'pdfToImage' },
  { name: 'password-generator', toolName: 'passwordGenerator' },
  { name: 'url-encoder', toolName: 'urlEncoder' },
  { name: 'timestamp-converter', toolName: 'timestampConverter' },
  { name: 'uuid-generator', toolName: 'uuidGenerator' },
  { name: 'hash-calculator', toolName: 'hashCalculator' },
  { name: 'unit-converter', toolName: 'unitConverter' },
  { name: 'fba-label-stamper', toolName: 'fbaLabelStamper' },
  { name: 'image-compressor', toolName: 'imageCompressor' },
  { name: 'json-formatter', toolName: 'jsonFormatter' },
  { name: 'qr-generator', toolName: 'qrGenerator' }
];

// 工具页面根目录
const toolsDir = path.join(process.cwd(), 'src/app/[locale]/tools');

function toPascalCase(str) {
  return str.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join('');
}

function convertToolPage(toolConfig) {
  const { name, toolName } = toolConfig;
  const toolDir = path.join(toolsDir, name);
  const pagePath = path.join(toolDir, 'page.tsx');
  const clientPath = path.join(toolDir, `${toPascalCase(name)}Client.tsx`);

  console.log(`Converting ${name}...`);

  // 检查页面文件是否存在
  if (!fs.existsSync(pagePath)) {
    console.log(`  ❌ Page file not found: ${pagePath}`);
    return;
  }

  // 读取原始页面内容
  const originalContent = fs.readFileSync(pagePath, 'utf8');

  // 检查是否已经是服务端组件（但可能有残留的客户端代码）
  const hasUseClient = originalContent.includes('"use client"');
  const hasMetadataImport = originalContent.includes('generateToolMetadata');

  if (!hasUseClient && hasMetadataImport) {
    console.log(`  ✅ Already converted: ${name}`);
    return;
  }

  // 如果有元数据导入但还有客户端代码，需要清理
  if (hasMetadataImport && hasUseClient) {
    console.log(`  🔧 Cleaning up mixed content: ${name}`);
    // 只保留服务端部分
    const lines = originalContent.split('\n');
    let serverEndIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('export default function') && lines[i].includes('Page()')) {
        // 找到下一个闭合括号
        let braceCount = 0;
        for (let j = i; j < lines.length; j++) {
          if (lines[j].includes('{')) braceCount++;
          if (lines[j].includes('}')) {
            braceCount--;
            if (braceCount === 0) {
              serverEndIndex = j;
              break;
            }
          }
        }
        break;
      }
    }

    if (serverEndIndex > 0) {
      const cleanContent = lines.slice(0, serverEndIndex + 1).join('\n');
      fs.writeFileSync(pagePath, cleanContent);
      console.log(`  ✅ Cleaned server page: ${pagePath}`);
    }
    return;
  }

  // 提取客户端代码（去掉 "use client" 和 export default）
  let clientContent = originalContent;

  // 移除 "use client" 指令
  clientContent = clientContent.replace(/^"use client";\s*\n/, '');

  // 找到最后一个 export default 并替换
  const exportDefaultRegex = /export default function \w+\(\)/;
  const match = clientContent.match(exportDefaultRegex);
  if (match) {
    const componentName = `${toPascalCase(name)}Client`;
    clientContent = clientContent.replace(
      exportDefaultRegex,
      `export default function ${componentName}()`
    );
  }

  // 添加 "use client" 指令到客户端组件
  clientContent = `"use client";\n\n${clientContent}`;

  // 创建新的服务端页面内容
  const componentName = `${toPascalCase(name)}Client`;
  const serverContent = `import { Metadata } from 'next';
import { generateToolMetadata } from '@/lib/metadata';
import ${componentName} from './${componentName}';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  return generateToolMetadata({
    locale,
    toolName: '${toolName}',
    path: '/tools/${name}'
  });
}

export default function ${toPascalCase(name)}Page() {
  return <${componentName} />;
}
`;

  try {
    // 写入客户端组件文件
    fs.writeFileSync(clientPath, clientContent);
    console.log(`  ✅ Created client component: ${clientPath}`);

    // 写入新的服务端页面文件
    fs.writeFileSync(pagePath, serverContent);
    console.log(`  ✅ Updated server page: ${pagePath}`);

  } catch (error) {
    console.error(`  ❌ Error converting ${name}:`, error.message);
  }
}

function main() {
  console.log('🚀 Starting tool page conversion...\n');

  // 检查工具目录是否存在
  if (!fs.existsSync(toolsDir)) {
    console.error(`❌ Tools directory not found: ${toolsDir}`);
    process.exit(1);
  }

  // 转换每个工具页面
  toolConfigs.forEach(convertToolPage);

  console.log('\n✨ Conversion completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Review the generated files');
  console.log('2. Test the development server');
  console.log('3. Commit the changes');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { convertToolPage, toolConfigs };
