"use client";


import { useState, useCallback, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Clock, Copy, Download, RotateCcw, Calendar, Timer } from 'lucide-react';
import { TopNavigation } from "@/components/TopNavigation";

export default function TimestampConverterClient() {
  const t = useTranslations('tools.timestampConverter');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [timestampInput, setTimestampInput] = useState('');
  const [dateInput, setDateInput] = useState('');
  const [conversionMode, setConversionMode] = useState<'toDate' | 'toTimestamp'>('toDate');
  const [result, setResult] = useState('');
  const [batchInput, setBatchInput] = useState('');
  const [batchResults, setBatchResults] = useState<Array<{timestamp: string, date: string}>>([]);
  const [copied, setCopied] = useState(false);
  const [timezone, setTimezone] = useState('UTC');

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Get user's timezone
  useEffect(() => {
    setTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }, []);

  const formatDate = useCallback((date: Date, tz: string = timezone) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: tz,
      timeZoneName: 'short'
    }).format(date);
  }, [timezone]);

  const getRelativeTime = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp * 1000;
    const seconds = Math.abs(diff) / 1000;
    
    if (seconds < 60) return `${Math.floor(seconds)} seconds ${diff > 0 ? 'ago' : 'from now'}`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes ${diff > 0 ? 'ago' : 'from now'}`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} hours ${diff > 0 ? 'ago' : 'from now'}`;
    if (seconds < 2592000) return `${Math.floor(seconds / 86400)} days ${diff > 0 ? 'ago' : 'from now'}`;
    if (seconds < 31536000) return `${Math.floor(seconds / 2592000)} months ${diff > 0 ? 'ago' : 'from now'}`;
    return `${Math.floor(seconds / 31536000)} years ${diff > 0 ? 'ago' : 'from now'}`;
  }, []);

  const convertTimestamp = useCallback(() => {
    if (conversionMode === 'toDate') {
      const timestamp = parseInt(timestampInput);
      if (isNaN(timestamp)) {
        setResult('Invalid timestamp');
        return;
      }
      
      // Handle both seconds and milliseconds
      const date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
      setResult(formatDate(date));
    } else {
      const date = new Date(dateInput);
      if (isNaN(date.getTime())) {
        setResult('Invalid date');
        return;
      }
      
      setResult(Math.floor(date.getTime() / 1000).toString());
    }
  }, [conversionMode, timestampInput, dateInput, formatDate]);

  const convertBatch = useCallback(() => {
    const timestamps = batchInput.split('\n').filter(line => line.trim());
    const results = timestamps.map(ts => {
      const timestamp = parseInt(ts.trim());
      if (isNaN(timestamp)) {
        return { timestamp: ts, date: 'Invalid timestamp' };
      }
      
      const date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
      return { timestamp: ts, date: formatDate(date) };
    });
    
    setBatchResults(results);
  }, [batchInput, formatDate]);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, []);

  const downloadCSV = useCallback(() => {
    if (batchResults.length === 0) return;
    
    const csv = 'Timestamp,Date\n' + batchResults.map(r => `${r.timestamp},"${r.date}"`).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'timestamp-conversion.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [batchResults]);

  const loadExample = useCallback((timestamp: string) => {
    setTimestampInput(timestamp);
    setConversionMode('toDate');
    setResult('');
  }, []);

  const clearAll = useCallback(() => {
    setTimestampInput('');
    setDateInput('');
    setResult('');
    setBatchInput('');
    setBatchResults([]);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Current Time */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Timer className="w-5 h-5 mr-2" />
              {t('current.title')}
            </h2>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">{t('current.timestamp')}</label>
                <div className="flex items-center space-x-2 mt-1">
                  <code className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm font-mono">
                    {Math.floor(currentTime.getTime() / 1000)}
                  </code>
                  <button
                    onClick={() => copyToClipboard(Math.floor(currentTime.getTime() / 1000).toString())}
                    className="px-2 py-2 text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">{t('current.date')}</label>
                <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm">
                  {formatDate(currentTime)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">{t('current.timezone')}</label>
                <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm">
                  {timezone}
                </div>
              </div>
            </div>
          </div>

          {/* Examples */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('examples.title')}</h2>
            <div className="space-y-3">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.now.title')}</h3>
                <button
                  onClick={() => loadExample(Math.floor(currentTime.getTime() / 1000).toString())}
                  className="text-sm text-blue-600 hover:text-blue-800 font-mono"
                >
                  {Math.floor(currentTime.getTime() / 1000)}
                </button>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.epoch.title')}</h3>
                <button
                  onClick={() => loadExample('0')}
                  className="text-sm text-green-600 hover:text-green-800 font-mono"
                >
                  0
                </button>
                <p className="text-xs text-gray-500 mt-1">{t('examples.epoch.date')}</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.y2k.title')}</h3>
                <button
                  onClick={() => loadExample('946684800')}
                  className="text-sm text-purple-600 hover:text-purple-800 font-mono"
                >
                  946684800
                </button>
                <p className="text-xs text-gray-500 mt-1">{t('examples.y2k.date')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Converter */}
        <div className="lg:col-span-2 space-y-6">
          {/* Single Conversion */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('converter.title')}</h2>
              <button
                onClick={clearAll}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Timestamp to Date */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="toDate"
                    checked={conversionMode === 'toDate'}
                    onChange={() => setConversionMode('toDate')}
                    className="text-blue-600"
                  />
                  <label htmlFor="toDate" className="font-medium text-gray-900">
                    {t('converter.timestampToDate')}
                  </label>
                </div>
                <input
                  type="text"
                  value={timestampInput}
                  onChange={(e) => setTimestampInput(e.target.value)}
                  placeholder={t('converter.timestampPlaceholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Date to Timestamp */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="toTimestamp"
                    checked={conversionMode === 'toTimestamp'}
                    onChange={() => setConversionMode('toTimestamp')}
                    className="text-blue-600"
                  />
                  <label htmlFor="toTimestamp" className="font-medium text-gray-900">
                    {t('converter.dateToTimestamp')}
                  </label>
                </div>
                <input
                  type="datetime-local"
                  value={dateInput}
                  onChange={(e) => setDateInput(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="mt-6 flex items-center space-x-4">
              <button
                onClick={convertTimestamp}
                disabled={!timestampInput && !dateInput}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Calendar className="w-4 h-4 mr-2 inline" />
                {t('converter.convert')}
              </button>
              
              {result && (
                <div className="flex items-center space-x-2 flex-1">
                  <div className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm font-mono">
                    {result}
                  </div>
                  <button
                    onClick={() => copyToClipboard(result)}
                    className="px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    {copied ? t('converter.copied') : t('converter.copy')}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Format Examples */}
          {timestampInput && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('formats.title')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(() => {
                  const timestamp = parseInt(timestampInput);
                  if (isNaN(timestamp)) return null;
                  
                  const date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
                  
                  return (
                    <>
                      <div>
                        <label className="text-sm font-medium text-gray-700">{t('formats.seconds')}</label>
                        <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm font-mono">
                          {Math.floor(date.getTime() / 1000)}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">{t('formats.milliseconds')}</label>
                        <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm font-mono">
                          {date.getTime()}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">{t('formats.iso')}</label>
                        <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm font-mono">
                          {date.toISOString()}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">{t('formats.relative')}</label>
                        <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded text-sm">
                          {getRelativeTime(Math.floor(date.getTime() / 1000))}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Batch Conversion */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('batch.title')}</h2>
            <p className="text-gray-600 mb-4">{t('batch.description')}</p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('batch.input')}
                </label>
                <textarea
                  value={batchInput}
                  onChange={(e) => setBatchInput(e.target.value)}
                  placeholder={t('batch.placeholder')}
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex items-center space-x-4">
                <button
                  onClick={convertBatch}
                  disabled={!batchInput.trim()}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {t('batch.convertAll')}
                </button>

                {batchResults.length > 0 && (
                  <button
                    onClick={downloadCSV}
                    className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-2 inline" />
                    {t('batch.download')}
                  </button>
                )}
              </div>

              {batchResults.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-md font-medium text-gray-900 mb-3">{t('batch.results')}</h3>
                  <div className="max-h-64 overflow-y-auto border border-gray-300 rounded-lg">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">Timestamp</th>
                          <th className="px-4 py-2 text-left font-medium text-gray-700">Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {batchResults.map((result, index) => (
                          <tr key={index} className="border-t border-gray-200">
                            <td className="px-4 py-2 font-mono text-gray-900">{result.timestamp}</td>
                            <td className="px-4 py-2 text-gray-700">{result.date}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Usage Guide */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('info.title')}</h2>
            <ul className="space-y-2">
              {t.raw('info.items').map((item: string, index: number) => (
                <li key={index} className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: item }} />
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
    </main>
    </div>
  );
}
