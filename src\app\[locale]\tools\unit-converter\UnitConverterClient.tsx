"use client";

import { useState, useCallback, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Cal<PERSON>tor, ArrowLeftRight, Copy, RotateCcw, Zap, Info } from 'lucide-react';
import { TopNavigation } from "@/components/TopNavigation";

// Unit conversion factors (all relative to base unit)
const conversionFactors = {
  length: {
    mm: 0.001,
    cm: 0.01,
    m: 1,
    km: 1000,
    in: 0.0254,
    ft: 0.3048,
    yd: 0.9144,
    mi: 1609.344
  },
  weight: {
    mg: 0.000001,
    g: 0.001,
    kg: 1,
    t: 1000,
    oz: 0.0283495,
    lb: 0.453592,
    st: 6.35029
  },
  area: {
    mm2: 0.000001,
    cm2: 0.0001,
    m2: 1,
    km2: 1000000,
    in2: 0.00064516,
    ft2: 0.092903,
    yd2: 0.836127,
    mi2: 2589988.11,
    acre: 4046.86,
    ha: 10000
  },
  volume: {
    ml: 0.001,
    l: 1,
    m3: 1000,
    in3: 0.0163871,
    ft3: 28.3168,
    gal: 3.78541,
    qt: 0.946353,
    pt: 0.473176,
    cup: 0.236588,
    fl_oz: 0.0295735
  },
  time: {
    ms: 0.001,
    s: 1,
    min: 60,
    h: 3600,
    d: 86400,
    w: 604800,
    mo: 2629746,
    y: 31556952
  },
  speed: {
    mps: 1,
    kph: 0.277778,
    mph: 0.44704,
    fps: 0.3048,
    knot: 0.514444
  },
  energy: {
    j: 1,
    kj: 1000,
    cal: 4.184,
    kcal: 4184,
    wh: 3600,
    kwh: 3600000,
    btu: 1055.06
  }
};

// Temperature conversion functions
const convertTemperature = (value: number, from: string, to: string): number => {
  if (from === to) return value;
  
  // Convert to Celsius first
  let celsius: number;
  switch (from) {
    case 'c': celsius = value; break;
    case 'f': celsius = (value - 32) * 5/9; break;
    case 'k': celsius = value - 273.15; break;
    case 'r': celsius = (value - 491.67) * 5/9; break;
    default: celsius = value;
  }
  
  // Convert from Celsius to target
  switch (to) {
    case 'c': return celsius;
    case 'f': return celsius * 9/5 + 32;
    case 'k': return celsius + 273.15;
    case 'r': return celsius * 9/5 + 491.67;
    default: return celsius;
  }
};

// Generic unit conversion
const convertUnit = (value: number, from: string, to: string, category: string): number => {
  if (category === 'temperature') {
    return convertTemperature(value, from, to);
  }
  
  const factors = conversionFactors[category as keyof typeof conversionFactors];
  if (!factors || !factors[from as keyof typeof factors] || !factors[to as keyof typeof factors]) {
    return value;
  }
  
  // Convert to base unit, then to target unit
  const baseValue = value * factors[from as keyof typeof factors];
  return baseValue / factors[to as keyof typeof factors];
};

interface QuickConversion {
  name: string;
  category: string;
  from: string;
  to: string;
  value: string;
}

export default function UnitConverterClient() {
  const t = useTranslations('tools.unitConverter');
  const [selectedCategory, setSelectedCategory] = useState('length');
  const [fromUnit, setFromUnit] = useState('m');
  const [toUnit, setToUnit] = useState('ft');
  const [fromValue, setFromValue] = useState('');
  const [toValue, setToValue] = useState('');
  const [copied, setCopied] = useState(false);

  // Get available units for selected category
  const getUnitsForCategory = useCallback((category: string) => {
    const unitKeys = Object.keys(conversionFactors[category as keyof typeof conversionFactors] || {});
    if (category === 'temperature') {
      return ['c', 'f', 'k', 'r'];
    }
    return unitKeys;
  }, []);

  // Update default units when category changes
  useEffect(() => {
    const units = getUnitsForCategory(selectedCategory);
    if (units.length >= 2) {
      setFromUnit(units[0]);
      setToUnit(units[1]);
    }
    setFromValue('');
    setToValue('');
  }, [selectedCategory, getUnitsForCategory]);

  // Convert value when inputs change
  useEffect(() => {
    if (fromValue && !isNaN(Number(fromValue))) {
      const result = convertUnit(Number(fromValue), fromUnit, toUnit, selectedCategory);
      setToValue(result.toFixed(6).replace(/\.?0+$/, ''));
    } else {
      setToValue('');
    }
  }, [fromValue, fromUnit, toUnit, selectedCategory]);

  const handleFromValueChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '' || /^-?\d*\.?\d*$/.test(value)) {
      setFromValue(value);
    }
  }, []);

  const swapUnits = useCallback(() => {
    setFromUnit(toUnit);
    setToUnit(fromUnit);
    setFromValue(toValue);
  }, [fromUnit, toUnit, toValue]);

  const clearValues = useCallback(() => {
    setFromValue('');
    setToValue('');
  }, []);

  const copyResult = useCallback(async () => {
    if (toValue) {
      try {
        await navigator.clipboard.writeText(toValue);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy:', error);
      }
    }
  }, [toValue]);

  const loadQuickConversion = useCallback((conversion: QuickConversion) => {
    setSelectedCategory(conversion.category);
    setFromUnit(conversion.from);
    setToUnit(conversion.to);
    setFromValue(conversion.value);
  }, []);

  const categories = ['length', 'weight', 'temperature', 'area', 'volume', 'time', 'speed', 'energy'];
  const quickConversions = t.raw('quickConvert.common') as QuickConversion[];

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Examples and Info */}
        <div className="lg:col-span-1 space-y-6">
          {/* Quick Conversions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              {t('quickConvert.title')}
            </h2>
            <p className="text-gray-600 mb-4">{t('quickConvert.description')}</p>
            <div className="space-y-2">
              {quickConversions.map((conversion, index) => (
                <button
                  key={index}
                  onClick={() => loadQuickConversion(conversion)}
                  className="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-blue-50 hover:border-blue-200 border border-transparent transition-colors"
                >
                  <div className="font-medium text-gray-900">{conversion.name}</div>
                  <div className="text-sm text-gray-600">
                    {conversion.value} {t(`units.${conversion.category}.${conversion.from}`).split(' ')[0]} → {t(`units.${conversion.category}.${conversion.to}`).split(' ')[0]}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Examples */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('examples.title')}</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.cooking.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.cooking.description')}</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.travel.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.travel.description')}</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.construction.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.construction.description')}</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.science.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.science.description')}</p>
              </div>
            </div>
          </div>

          {/* Info */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Info className="w-5 h-5 mr-2" />
              {t('info.title')}
            </h2>
            <ul className="space-y-2">
              {t.raw('info.items').map((item: string, index: number) => (
                <li key={index} className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: item }} />
              ))}
            </ul>
          </div>
        </div>

        {/* Main Converter */}
        <div className="lg:col-span-2 space-y-6">
          {/* Category Selection */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('converter.category')}</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`p-3 rounded-lg border text-center transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t(`categories.${category}`)}
                </button>
              ))}
            </div>
          </div>

          {/* Unit Converter */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('converter.title')}</h2>
              <button
                onClick={clearValues}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>
            <p className="text-gray-600 mb-6">{t('converter.description')}</p>

            <div className="space-y-6">
              {/* From Unit */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('converter.fromUnit')}
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <select
                    value={fromUnit}
                    onChange={(e) => setFromUnit(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {getUnitsForCategory(selectedCategory).map((unit) => (
                      <option key={unit} value={unit}>
                        {t(`units.${selectedCategory}.${unit}`)}
                      </option>
                    ))}
                  </select>
                  <input
                    type="text"
                    value={fromValue}
                    onChange={handleFromValueChange}
                    placeholder={t('converter.fromPlaceholder')}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Swap Button */}
              <div className="flex justify-center">
                <button
                  onClick={swapUnits}
                  className="p-2 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors"
                  title={t('converter.swap')}
                >
                  <ArrowLeftRight className="w-5 h-5" />
                </button>
              </div>

              {/* To Unit */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('converter.toUnit')}
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <select
                    value={toUnit}
                    onChange={(e) => setToUnit(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {getUnitsForCategory(selectedCategory).map((unit) => (
                      <option key={unit} value={unit}>
                        {t(`units.${selectedCategory}.${unit}`)}
                      </option>
                    ))}
                  </select>
                  <div className="relative">
                    <input
                      type="text"
                      value={toValue}
                      readOnly
                      placeholder={t('converter.toValue')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-900"
                    />
                    {toValue && (
                      <button
                        onClick={copyResult}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-500 hover:text-blue-600 transition-colors"
                        title={t('converter.copy')}
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Result Display */}
              {toValue && (
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-lg font-semibold text-green-800">
                        {fromValue} {t(`units.${selectedCategory}.${fromUnit}`).split(' ')[0]} = {toValue} {t(`units.${selectedCategory}.${toUnit}`).split(' ')[0]}
                      </div>
                      <div className="text-sm text-green-600 mt-1">
                        {t(`categories.${selectedCategory}`)} • {t('converter.title')}
                      </div>
                    </div>
                    <button
                      onClick={copyResult}
                      className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                    >
                      <Copy className="w-4 h-4 mr-1 inline" />
                      {copied ? t('converter.copied') : t('converter.copy')}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Conversion Table */}
          {fromValue && !isNaN(Number(fromValue)) && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('converter.title')} - {t(`categories.${selectedCategory}`)}
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-2 font-medium text-gray-900">{t('converter.toUnit')}</th>
                      <th className="text-right py-2 font-medium text-gray-900">{t('converter.toValue')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {getUnitsForCategory(selectedCategory).map((unit) => {
                      const convertedValue = convertUnit(Number(fromValue), fromUnit, unit, selectedCategory);
                      return (
                        <tr key={unit} className="border-b border-gray-100">
                          <td className="py-2 text-gray-700">
                            {t(`units.${selectedCategory}.${unit}`)}
                          </td>
                          <td className="py-2 text-right font-mono text-gray-900">
                            {convertedValue.toFixed(6).replace(/\.?0+$/, '')}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    </main>
    </div>
  );
}
