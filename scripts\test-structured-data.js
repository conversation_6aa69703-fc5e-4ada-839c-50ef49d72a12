const { JSDOM } = require('jsdom');

async function testStructuredData() {
  const languages = ['zh', 'en', 'ja', 'de', 'ru'];
  
  for (const lang of languages) {
    console.log(`\n=== Testing ${lang.toUpperCase()} ===`);
    
    try {
      const response = await fetch(`http://localhost:3000/${lang}`);
      const html = await response.text();
      
      // 查找结构化数据
      const dom = new JSDOM(html);
      const scripts = dom.window.document.querySelectorAll('script[type="application/ld+json"]');
      
      console.log(`Found ${scripts.length} structured data scripts`);
      
      scripts.forEach((script, index) => {
        try {
          const data = JSON.parse(script.textContent);
          console.log(`\nScript ${index + 1}:`);
          console.log(`Type: ${data['@type']}`);
          
          if (data['@type'] === 'WebSite') {
            console.log(`Name: ${data.name}`);
            console.log(`AlternateName: ${data.alternateName}`);
            console.log(`Description: ${data.description}`);
            console.log(`Language: ${data.inLanguage}`);
          } else if (data['@type'] === 'Organization') {
            console.log(`Name: ${data.name}`);
            console.log(`Description: ${data.description}`);
            console.log(`Contact Type: ${data.contactPoint?.contactType}`);
            console.log(`Available Languages: ${data.contactPoint?.availableLanguage?.join(', ')}`);
          }
        } catch (e) {
          console.error(`Error parsing script ${index + 1}:`, e.message);
        }
      });
      
    } catch (error) {
      console.error(`Error testing ${lang}:`, error.message);
    }
  }
}

// 检查是否有 fetch 和 JSDOM
if (typeof fetch === 'undefined') {
  console.log('Installing node-fetch...');
  require('child_process').execSync('npm install node-fetch@2 jsdom', { stdio: 'inherit' });
  global.fetch = require('node-fetch');
}

testStructuredData().catch(console.error);
