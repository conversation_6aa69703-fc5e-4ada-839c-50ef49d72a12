// 简单的结构化数据验证脚本
const { generateWebsiteSchema, generateOrganizationSchema } = require('../src/lib/structured-data');

// 模拟翻译函数
function createMockTranslation(locale) {
  const translations = {
    zh: {
      'website.alternateName': 'AnyTool - 免费在线工具集',
      'website.description': '提供图片处理、文本处理、数据转换、PDF操作等多种实用在线工具，完全免费使用',
      'organization.description': '提供免费在线工具的专业平台',
      'organization.contactType': '客户服务'
    },
    en: {
      'website.alternateName': 'AnyTool - Free Online Tools Collection',
      'website.description': 'Free online tools for image processing, text processing, data conversion, PDF operations and more',
      'organization.description': 'Professional platform providing free online tools',
      'organization.contactType': 'customer service'
    },
    ja: {
      'website.alternateName': 'AnyTool - 無料オンラインツール集',
      'website.description': '画像処理、テキスト処理、データ変換、PDF操作など、様々な実用的なオンラインツールを完全無料で提供',
      'organization.description': '無料オンラインツールを提供する専門プラットフォーム',
      'organization.contactType': 'カスタマーサービス'
    },
    de: {
      'website.alternateName': 'AnyTool - Kostenlose Online-Tools Sammlung',
      'website.description': 'Kostenlose Online-Tools für Bildbearbeitung, Textverarbeitung, Datenkonvertierung, PDF-Operationen und mehr',
      'organization.description': 'Professionelle Plattform für kostenlose Online-Tools',
      'organization.contactType': 'Kundendienst'
    },
    ru: {
      'website.alternateName': 'AnyTool - Коллекция бесплатных онлайн-инструментов',
      'website.description': 'Бесплатные онлайн-инструменты для обработки изображений, обработки текста, конвертации данных, операций с PDF и многое другое',
      'organization.description': 'Профессиональная платформа, предоставляющая бесплатные онлайн-инструменты',
      'organization.contactType': 'служба поддержки клиентов'
    }
  };
  
  return function(key) {
    return translations[locale][key] || `[Missing: ${key}]`;
  };
}

async function validateStructuredData() {
  const languages = ['zh', 'en', 'ja', 'de', 'ru'];
  
  console.log('=== Structured Data Validation ===\n');
  
  for (const locale of languages) {
    console.log(`--- ${locale.toUpperCase()} ---`);
    
    const t = createMockTranslation(locale);
    
    try {
      // 测试网站结构化数据
      const websiteSchema = await generateWebsiteSchema(locale, t);
      console.log('✅ Website Schema:');
      console.log(`   Name: ${websiteSchema.name}`);
      console.log(`   AlternateName: ${websiteSchema.alternateName}`);
      console.log(`   Description: ${websiteSchema.description}`);
      console.log(`   Language: ${websiteSchema.inLanguage}`);
      
      // 测试组织结构化数据
      const orgSchema = await generateOrganizationSchema(locale, t);
      console.log('✅ Organization Schema:');
      console.log(`   Name: ${orgSchema.name}`);
      console.log(`   Description: ${orgSchema.description}`);
      console.log(`   Contact Type: ${orgSchema.contactPoint.contactType}`);
      console.log(`   Available Languages: ${orgSchema.contactPoint.availableLanguage.join(', ')}`);
      
    } catch (error) {
      console.error(`❌ Error for ${locale}:`, error.message);
    }
    
    console.log('');
  }
}

validateStructuredData().catch(console.error);
