import { Tool } from '@/config/tools';

// 网站基础结构化数据
export function generateWebsiteSchema(locale: string) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AnyTool",
    "alternateName": "AnyTool - 免费在线工具集",
    "url": `${baseUrl}/${locale}`,
    "description": locale === 'zh' 
      ? "提供图片处理、文本处理、数据转换、PDF操作等多种实用在线工具，完全免费使用"
      : "Free online tools for image processing, text processing, data conversion, PDF operations and more",
    "inLanguage": locale === 'zh' ? 'zh-CN' : 'en-US',
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/${locale}?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "AnyTool",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 512,
        "height": 512
      }
    }
  };
}

// 组织结构化数据
export function generateOrganizationSchema() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AnyTool",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/logo.png`,
      "width": 512,
      "height": 512
    },
    "description": "提供免费在线工具的专业平台",
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["Chinese", "English"]
    },
    "sameAs": [
      // 这里可以添加社交媒体链接
    ]
  };
}

// 工具页面的软件应用结构化数据
export function generateSoftwareApplicationSchema(
  tool: Tool, 
  locale: string,
  toolName: string,
  toolDescription: string
) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  const toolUrl = `${baseUrl}/${locale}${tool.path}`;
  
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": toolName,
    "description": toolDescription,
    "url": toolUrl,
    "applicationCategory": "WebApplication",
    "operatingSystem": "Any",
    "isAccessibleForFree": true,
    "inLanguage": locale === 'zh' ? 'zh-CN' : 'en-US',
    "softwareVersion": "1.0",
    "requirements": "JavaScript enabled browser",
    "author": {
      "@type": "Organization",
      "name": "AnyTool",
      "url": baseUrl
    },
    "publisher": {
      "@type": "Organization",
      "name": "AnyTool",
      "url": baseUrl
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      locale === 'zh' ? "免费使用" : "Free to use",
      locale === 'zh' ? "无需注册" : "No registration required",
      locale === 'zh' ? "浏览器内处理" : "Browser-based processing",
      locale === 'zh' ? "隐私安全" : "Privacy secure"
    ]
  };
}

// 面包屑导航结构化数据
export function generateBreadcrumbSchema(
  breadcrumbs: Array<{ name: string; url: string }>,
  locale: string
) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url.startsWith('http') ? crumb.url : `${baseUrl}${crumb.url}`
    }))
  };
}

// FAQ结构化数据
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// 工具集合页面的结构化数据
export function generateItemListSchema(
  tools: Tool[],
  categoryName: string,
  locale: string
) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  
  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": `${categoryName}工具集合`,
    "description": `${categoryName}相关的在线工具集合`,
    "numberOfItems": tools.length,
    "itemListElement": tools.map((tool, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "SoftwareApplication",
        "name": tool.name,
        "description": tool.description,
        "url": `${baseUrl}/${locale}${tool.path}`,
        "applicationCategory": "WebApplication"
      }
    }))
  };
}
