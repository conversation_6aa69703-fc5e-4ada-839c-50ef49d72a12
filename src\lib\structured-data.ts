import { Tool } from '@/config/tools';
import { Locale } from '@/i18n/config';

// 语言代码映射 - 符合 Schema.org 标准
const schemaLanguageMap: Record<Locale, string> = {
  zh: 'zh-CN',
  en: 'en-US',
  ja: 'ja-JP',
  de: 'de-DE',
  ru: 'ru-RU'
};

// 语言名称映射 - 用于 availableLanguage 字段
const schemaLanguageNames: Record<Locale, string> = {
  zh: 'Chinese',
  en: 'English',
  ja: 'Japanese',
  de: 'German',
  ru: 'Russian'
};

// 获取 Schema.org 语言代码
function getSchemaLanguage(locale: string): string {
  return schemaLanguageMap[locale as Locale] || schemaLanguageMap.en;
}

// 获取所有支持的语言名称列表
function getAllSchemaLanguages(): string[] {
  return Object.values(schemaLanguageNames);
}

// 网站基础结构化数据
export async function generateWebsiteSchema(locale: string, t: any) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';

  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AnyTool",
    "alternateName": t('website.alternateName'),
    "url": `${baseUrl}/${locale}`,
    "description": t('website.description'),
    "inLanguage": getSchemaLanguage(locale),
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/${locale}?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "AnyTool",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 512,
        "height": 512
      }
    }
  };
}

// 组织结构化数据
export async function generateOrganizationSchema(locale: string, t: any) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';

  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AnyTool",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/logo.png`,
      "width": 512,
      "height": 512
    },
    "description": t('organization.description'),
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": t('organization.contactType'),
      "availableLanguage": getAllSchemaLanguages()
    },
    "sameAs": [
      // 这里可以添加社交媒体链接
    ]
  };
}

// 工具页面的软件应用结构化数据
export async function generateSoftwareApplicationSchema(
  tool: Tool,
  locale: string,
  toolName: string,
  toolDescription: string,
  t: any
) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  const toolUrl = `${baseUrl}/${locale}${tool.path}`;

  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": toolName,
    "description": toolDescription,
    "url": toolUrl,
    "applicationCategory": "WebApplication",
    "operatingSystem": "Any",
    "isAccessibleForFree": true,
    "inLanguage": getSchemaLanguage(locale),
    "softwareVersion": "1.0",
    "requirements": t('software.requirements'),
    "author": {
      "@type": "Organization",
      "name": "AnyTool",
      "url": baseUrl
    },
    "publisher": {
      "@type": "Organization",
      "name": "AnyTool",
      "url": baseUrl
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      t('software.features.freeToUse'),
      t('software.features.noRegistration'),
      t('software.features.browserBased'),
      t('software.features.privacySecure')
    ]
  };
}

// 面包屑导航结构化数据
export function generateBreadcrumbSchema(
  breadcrumbs: Array<{ name: string; url: string }>,
  locale: string
) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url.startsWith('http') ? crumb.url : `${baseUrl}${crumb.url}`
    }))
  };
}

// FAQ结构化数据
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// 工具集合页面的结构化数据
export async function generateItemListSchema(
  tools: Tool[],
  categoryName: string,
  locale: string,
  t: any
) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://anytool.app';

  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": `${categoryName}${t('itemList.toolsCollection')}`,
    "description": `${t('itemList.toolsDescription')}${categoryName}`,
    "numberOfItems": tools.length,
    "itemListElement": tools.map((tool, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "SoftwareApplication",
        "name": tool.name,
        "description": tool.description,
        "url": `${baseUrl}/${locale}${tool.path}`,
        "applicationCategory": "WebApplication"
      }
    }))
  };
}
