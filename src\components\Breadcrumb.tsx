"use client";

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface BreadcrumbItem {
  name: string;
  href?: string;
  isCurrentPage?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  const t = useTranslations('navigation');

  return (
    <nav 
      aria-label="面包屑导航" 
      className={cn("flex items-center space-x-1 text-sm text-gray-600", className)}
    >
      <ol className="flex items-center space-x-1">
        {/* 首页链接 */}
        <li>
          <Link 
            href="/" 
            className="flex items-center hover:text-gray-900 transition-colors"
            aria-label="返回首页"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">首页</span>
          </Link>
        </li>

        {/* 面包屑项目 */}
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            <ChevronRight className="h-4 w-4 mx-1 text-gray-400" />
            {item.href && !item.isCurrentPage ? (
              <Link 
                href={item.href}
                className="hover:text-gray-900 transition-colors"
              >
                {item.name}
              </Link>
            ) : (
              <span 
                className={cn(
                  item.isCurrentPage 
                    ? "text-gray-900 font-medium" 
                    : "text-gray-600"
                )}
                aria-current={item.isCurrentPage ? "page" : undefined}
              >
                {item.name}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// 工具页面专用的面包屑生成函数
export function generateToolBreadcrumbs(
  toolName: string,
  categoryName: string,
  locale: string
): BreadcrumbItem[] {
  return [
    {
      name: categoryName,
      href: `/${locale}#${categoryName.toLowerCase()}`
    },
    {
      name: toolName,
      isCurrentPage: true
    }
  ];
}
