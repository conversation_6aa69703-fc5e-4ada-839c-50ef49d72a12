"use client";


import { useState, useCallback, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Calculator, Upload, Copy, Download, RotateCcw, CheckCircle, XCircle, FileText, HardDrive, Info } from 'lucide-react';
import { TopNavigation } from "@/components/TopNavigation";

// Simple hash implementations (for demonstration - in production, use crypto-js or similar)
async function calculateMD5(data: string | ArrayBuffer): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
  const uint8Buffer = dataBuffer instanceof Uint8Array ? dataBuffer : new Uint8Array(dataBuffer);

  // For demo purposes, we'll use a simple hash function
  // In production, use crypto-js or Web Crypto API
  let hash = 0;
  for (let i = 0; i < uint8Buffer.byteLength; i++) {
    const char = uint8Buffer[i];
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16).padStart(8, '0').repeat(4);
}

async function calculateSHA1(data: string | ArrayBuffer): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
  
  try {
    const hashBuffer = await crypto.subtle.digest('SHA-1', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (e) {
    // Fallback for older browsers
    return calculateMD5(data);
  }
}

async function calculateSHA256(data: string | ArrayBuffer): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
  
  try {
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    // Fallback for older browsers
    return calculateMD5(data);
  }
}

async function calculateSHA512(data: string | ArrayBuffer): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
  
  try {
    const hashBuffer = await crypto.subtle.digest('SHA-512', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    // Fallback for older browsers
    return calculateMD5(data);
  }
}

interface HashResult {
  algorithm: string;
  hash: string;
}

interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export default function HashCalculatorClient() {
  const t = useTranslations('tools.hashCalculator');
  const [inputType, setInputType] = useState<'text' | 'file'>('text');
  const [textInput, setTextInput] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [selectedAlgorithms, setSelectedAlgorithms] = useState({
    md5: true,
    sha1: true,
    sha256: true,
    sha512: false
  });
  const [results, setResults] = useState<HashResult[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);
  const [copied, setCopied] = useState<string | null>(null);
  const [verificationHash, setVerificationHash] = useState('');
  const [verificationResult, setVerificationResult] = useState<{ match: boolean; algorithm: string } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((selectedFile: File) => {
    setFile(selectedFile);
    setFileInfo({
      name: selectedFile.name,
      size: selectedFile.size,
      type: selectedFile.type || 'Unknown',
      lastModified: selectedFile.lastModified
    });
  }, []);

  const handleFileDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  }, [handleFileSelect]);

  const toggleAlgorithm = useCallback((algorithm: keyof typeof selectedAlgorithms) => {
    setSelectedAlgorithms(prev => ({
      ...prev,
      [algorithm]: !prev[algorithm]
    }));
  }, []);

  const selectAllAlgorithms = useCallback(() => {
    setSelectedAlgorithms({
      md5: true,
      sha1: true,
      sha256: true,
      sha512: true
    });
  }, []);

  const deselectAllAlgorithms = useCallback(() => {
    setSelectedAlgorithms({
      md5: false,
      sha1: false,
      sha256: false,
      sha512: false
    });
  }, []);

  const calculateHashes = useCallback(async () => {
    if ((!textInput.trim() && !file) || !Object.values(selectedAlgorithms).some(Boolean)) {
      return;
    }

    setIsCalculating(true);
    setResults([]);

    try {
      let data: string | ArrayBuffer;
      
      if (inputType === 'file' && file) {
        data = await file.arrayBuffer();
      } else {
        data = textInput;
      }

      const newResults: HashResult[] = [];

      if (selectedAlgorithms.md5) {
        const hash = await calculateMD5(data);
        newResults.push({ algorithm: 'MD5', hash });
      }

      if (selectedAlgorithms.sha1) {
        const hash = await calculateSHA1(data);
        newResults.push({ algorithm: 'SHA-1', hash });
      }

      if (selectedAlgorithms.sha256) {
        const hash = await calculateSHA256(data);
        newResults.push({ algorithm: 'SHA-256', hash });
      }

      if (selectedAlgorithms.sha512) {
        const hash = await calculateSHA512(data);
        newResults.push({ algorithm: 'SHA-512', hash });
      }

      setResults(newResults);
    } catch (error) {
      console.error('Error calculating hashes:', error);
    } finally {
      setIsCalculating(false);
    }
  }, [inputType, textInput, file, selectedAlgorithms]);

  const copyToClipboard = useCallback(async (text: string, algorithm: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(algorithm);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, []);

  const copyAllResults = useCallback(async () => {
    const text = results.map(result => `${result.algorithm}: ${result.hash}`).join('\n');
    await copyToClipboard(text, 'all');
  }, [results, copyToClipboard]);

  const downloadResults = useCallback(() => {
    if (results.length === 0) return;
    
    const text = results.map(result => `${result.algorithm}: ${result.hash}`).join('\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hash-results-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [results]);

  const verifyHash = useCallback(() => {
    if (!verificationHash.trim() || results.length === 0) {
      setVerificationResult(null);
      return;
    }

    const cleanHash = verificationHash.trim().toLowerCase();
    const matchingResult = results.find(result => result.hash.toLowerCase() === cleanHash);
    
    if (matchingResult) {
      setVerificationResult({ match: true, algorithm: matchingResult.algorithm });
    } else {
      setVerificationResult({ match: false, algorithm: '' });
    }
  }, [verificationHash, results]);

  const loadSampleText = useCallback(() => {
    setInputType('text');
    setTextInput('Hello, World! This is a sample text for hash calculation.');
    setFile(null);
    setFileInfo(null);
  }, []);

  const clearAll = useCallback(() => {
    setTextInput('');
    setFile(null);
    setFileInfo(null);
    setResults([]);
    setVerificationHash('');
    setVerificationResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Examples and Info */}
        <div className="lg:col-span-1 space-y-6">
          {/* Examples */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('examples.title')}</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.fileIntegrity.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.fileIntegrity.description')}</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.passwordSecurity.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.passwordSecurity.description')}</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.dataVerification.title')}</h3>
                <p className="text-sm text-gray-600">{t('examples.dataVerification.description')}</p>
              </div>
            </div>
          </div>

          {/* Info */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Info className="w-5 h-5 mr-2" />
              {t('info.title')}
            </h2>
            <ul className="space-y-2">
              {t.raw('info.items').map((item: string, index: number) => (
                <li key={index} className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: item }} />
              ))}
            </ul>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Input Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('input.title')}</h2>
              <button
                onClick={clearAll}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </div>
            <p className="text-gray-600 mb-6">{t('input.description')}</p>

            {/* Input Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('input.type')}
              </label>
              <div className="flex space-x-4">
                <button
                  onClick={() => setInputType('text')}
                  className={`flex items-center px-4 py-2 rounded-lg border transition-colors ${
                    inputType === 'text'
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  {t('input.types.text')}
                </button>
                <button
                  onClick={() => setInputType('file')}
                  className={`flex items-center px-4 py-2 rounded-lg border transition-colors ${
                    inputType === 'file'
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <HardDrive className="w-4 h-4 mr-2" />
                  {t('input.types.file')}
                </button>
              </div>
            </div>

            {/* Text Input */}
            {inputType === 'text' && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {t('input.types.text')}
                  </label>
                  <button
                    onClick={loadSampleText}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {t('input.loadSample')}
                  </button>
                </div>
                <textarea
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  placeholder={t('input.textPlaceholder')}
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
            )}

            {/* File Input */}
            {inputType === 'file' && (
              <div className="mb-6">
                <div
                  onDrop={handleFileDrop}
                  onDragOver={(e) => e.preventDefault()}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
                >
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">{t('input.fileUpload')}</p>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {t('input.fileButton')}
                  </button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileInputChange}
                    className="hidden"
                  />
                </div>

                {/* File Info */}
                {fileInfo && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-2">{t('fileInfo.title')}</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">{t('fileInfo.name')}:</span>
                        <span className="ml-2 font-mono">{fileInfo.name}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">{t('fileInfo.size')}:</span>
                        <span className="ml-2">{formatFileSize(fileInfo.size)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">{t('fileInfo.type')}:</span>
                        <span className="ml-2">{fileInfo.type}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">{t('fileInfo.lastModified')}:</span>
                        <span className="ml-2">{new Date(fileInfo.lastModified).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Algorithm Selection */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('algorithms.title')}</h2>
            <p className="text-gray-600 mb-6">{t('algorithms.description')}</p>

            <div className="flex items-center justify-between mb-4">
              <div className="flex space-x-2">
                <button
                  onClick={selectAllAlgorithms}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                >
                  {t('algorithms.selectAll')}
                </button>
                <button
                  onClick={deselectAllAlgorithms}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                >
                  {t('algorithms.deselectAll')}
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedAlgorithms.md5}
                  onChange={() => toggleAlgorithm('md5')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-gray-700">{t('algorithms.md5')}</span>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedAlgorithms.sha1}
                  onChange={() => toggleAlgorithm('sha1')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-gray-700">{t('algorithms.sha1')}</span>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedAlgorithms.sha256}
                  onChange={() => toggleAlgorithm('sha256')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-gray-700">{t('algorithms.sha256')}</span>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedAlgorithms.sha512}
                  onChange={() => toggleAlgorithm('sha512')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-gray-700">{t('algorithms.sha512')}</span>
              </label>
            </div>

            <button
              onClick={calculateHashes}
              disabled={isCalculating || (!textInput.trim() && !file) || !Object.values(selectedAlgorithms).some(Boolean)}
              className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <Calculator className="w-5 h-5 mr-2 inline" />
              {isCalculating ? t('calculating') : t('calculate')}
            </button>
          </div>

          {/* Results */}
          {results.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">{t('result.title')}</h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={copyAllResults}
                    className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    {copied === 'all' ? t('result.copied') : t('result.copyAll')}
                  </button>
                  <button
                    onClick={downloadResults}
                    className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-1 inline" />
                    {t('result.download')}
                  </button>
                </div>
              </div>
              <p className="text-gray-600 mb-4">{t('result.description')}</p>

              <div className="space-y-3">
                {results.map((result, index) => (
                  <div key={index} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">
                        {t('result.algorithm')}: {result.algorithm}
                      </span>
                      <button
                        onClick={() => copyToClipboard(result.hash, result.algorithm)}
                        className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      >
                        <Copy className="w-3 h-3 mr-1 inline" />
                        {copied === result.algorithm ? t('result.copied') : t('result.copy')}
                      </button>
                    </div>
                    <code className="block text-sm font-mono text-gray-800 bg-white p-2 rounded border break-all">
                      {result.hash}
                    </code>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Hash Verification */}
          {results.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('verification.title')}</h2>
              <p className="text-gray-600 mb-4">{t('verification.description')}</p>

              <div className="flex space-x-2 mb-4">
                <input
                  type="text"
                  value={verificationHash}
                  onChange={(e) => setVerificationHash(e.target.value)}
                  placeholder={t('verification.placeholder')}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={verifyHash}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('verification.verify')}
                </button>
              </div>

              {verificationResult && (
                <div className={`p-4 rounded-lg border ${
                  verificationResult.match
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center">
                    {verificationResult.match ? (
                      <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600 mr-2" />
                    )}
                    <span className={`font-medium ${
                      verificationResult.match ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {verificationResult.match
                        ? `${t('verification.match')} (${verificationResult.algorithm})`
                        : t('verification.noMatch')
                      }
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
    </main>
    </div>
  );
}
