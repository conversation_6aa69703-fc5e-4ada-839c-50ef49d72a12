"use client";


import { useState, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { Copy, Download, RotateCcw, FileText, Globe, Code } from 'lucide-react';
import { TopNavigation } from "@/components/TopNavigation";

export default function UrlEncoderClient() {
  const t = useTranslations('tools.urlEncoder');
  const [input, setInput] = useState('');
  const [result, setResult] = useState('');
  const [copied, setCopied] = useState(false);

  // Sample data for demonstration
  const sampleData = {
    url: 'https://example.com/search?q=hello world&lang=en',
    component: 'hello world & special chars!',
    chinese: 'https://example.com/search?q=你好世界&lang=zh'
  };

  const loadSample = useCallback((type: 'url' | 'component' | 'chinese') => {
    setInput(sampleData[type]);
    setResult('');
  }, []);

  const clearInput = useCallback(() => {
    setInput('');
    setResult('');
  }, []);

  // URL encoding/decoding functions
  const encodeURL = useCallback(() => {
    try {
      const encoded = encodeURI(input);
      setResult(encoded);
    } catch (error) {
      setResult('Error: Invalid input for URL encoding');
    }
  }, [input]);

  const decodeURL = useCallback(() => {
    try {
      const decoded = decodeURI(input);
      setResult(decoded);
    } catch (error) {
      setResult('Error: Invalid input for URL decoding');
    }
  }, [input]);

  const encodeComponent = useCallback(() => {
    try {
      const encoded = encodeURIComponent(input);
      setResult(encoded);
    } catch (error) {
      setResult('Error: Invalid input for component encoding');
    }
  }, [input]);

  const decodeComponent = useCallback(() => {
    try {
      const decoded = decodeURIComponent(input);
      setResult(decoded);
    } catch (error) {
      setResult('Error: Invalid input for component decoding');
    }
  }, [input]);

  const encodeBase64 = useCallback(() => {
    try {
      const encoded = btoa(unescape(encodeURIComponent(input)));
      setResult(encoded);
    } catch (error) {
      setResult('Error: Invalid input for Base64 encoding');
    }
  }, [input]);

  const decodeBase64 = useCallback(() => {
    try {
      const decoded = decodeURIComponent(escape(atob(input)));
      setResult(decoded);
    } catch (error) {
      setResult('Error: Invalid input for Base64 decoding');
    }
  }, [input]);

  const copyToClipboard = useCallback(async () => {
    if (!result) return;
    
    try {
      await navigator.clipboard.writeText(result);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, [result]);

  const downloadResult = useCallback(() => {
    if (!result) return;
    
    const blob = new Blob([result], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'url-encoded-result.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [result]);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>


      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          {/* Input */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('input.title')}</h2>
              <div className="flex space-x-2">
                <button
                  onClick={() => loadSample('url')}
                  className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                >
                  URL
                </button>
                <button
                  onClick={() => loadSample('component')}
                  className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                >
                  Component
                </button>
                <button
                  onClick={() => loadSample('chinese')}
                  className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors"
                >
                  中文
                </button>
                <button
                  onClick={clearInput}
                  className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                >
                  <RotateCcw className="w-3 h-3" />
                </button>
              </div>
            </div>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={t('input.placeholder')}
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-2">{t('input.description')}</p>
          </div>

          {/* Actions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('actions.title')}</h2>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={encodeURL}
                disabled={!input.trim()}
                className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Code className="w-4 h-4 mr-2" />
                {t('actions.encode')}
              </button>
              <button
                onClick={decodeURL}
                disabled={!input.trim()}
                className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                {t('actions.decode')}
              </button>
              <button
                onClick={encodeComponent}
                disabled={!input.trim()}
                className="flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Code className="w-4 h-4 mr-2" />
                {t('actions.encodeComponent')}
              </button>
              <button
                onClick={decodeComponent}
                disabled={!input.trim()}
                className="flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                {t('actions.decodeComponent')}
              </button>
              <button
                onClick={encodeBase64}
                disabled={!input.trim()}
                className="flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Code className="w-4 h-4 mr-2" />
                {t('actions.encodeBase64')}
              </button>
              <button
                onClick={decodeBase64}
                disabled={!input.trim()}
                className="flex items-center justify-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                {t('actions.decodeBase64')}
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-3">{t('actions.description')}</p>
          </div>
        </div>

        {/* Result Section */}
        <div className="space-y-6">
          {/* Result */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('result.title')}</h2>
              <div className="flex space-x-2">
                <button
                  onClick={copyToClipboard}
                  disabled={!result}
                  className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  <Copy className="w-4 h-4 mr-1" />
                  {copied ? t('result.copied') : t('result.copy')}
                </button>
                <button
                  onClick={downloadResult}
                  disabled={!result}
                  className="flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  <Download className="w-4 h-4 mr-1" />
                  {t('result.download')}
                </button>
              </div>
            </div>
            <textarea
              value={result}
              readOnly
              placeholder={t('result.placeholder')}
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-2">{t('result.description')}</p>
          </div>

          {/* Examples */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('examples.title')}</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.url.title')}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{t('examples.url.original')}</span>
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  <span className="font-mono text-xs bg-blue-50 px-2 py-1 rounded">{t('examples.url.encoded')}</span>
                </p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-medium text-gray-900">{t('examples.component.title')}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{t('examples.component.original')}</span>
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  <span className="font-mono text-xs bg-green-50 px-2 py-1 rounded">{t('examples.component.encoded')}</span>
                </p>
              </div>
            </div>
          </div>

          {/* Usage Guide */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('info.title')}</h2>
            <ul className="space-y-2">
              {t.raw('info.items').map((item: string, index: number) => (
                <li key={index} className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: item }} />
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
    </main>
    </div>
  );
}
