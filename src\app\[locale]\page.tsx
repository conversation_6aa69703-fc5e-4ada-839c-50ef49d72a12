"use client";

import Link from "next/link";
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import {
  Image,
  FileText,
  Database,
  Wrench,
  FileType,
  ArrowRight,
  Star,
  Users,
  Zap
} from "lucide-react";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toolCategories, getToolsByCategory } from "@/config/tools";

const categoryIcons = {
  image: Image,
  text: FileText,
  data: Database,
  utility: Wrench,
  pdf: FileType,
};

export default function Home() {
  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations('home');
  const tNav = useTranslations('navigation');

  const features = [
    {
      icon: Zap,
      title: t('features.fast.title'),
      description: t('features.fast.description')
    },
    {
      icon: Star,
      title: t('features.free.title'),
      description: t('features.free.description')
    },
    {
      icon: Users,
      title: t('features.easy.title'),
      description: t('features.easy.description')
    }
  ];
  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />

      <main>
        {/* Hero Section */}
        <section className="px-6 py-12 md:py-20">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                {t('hero.title')}
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8">
              {t('hero.subtitle')}
            </p>
            <p className="text-lg text-muted-foreground mb-8">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="#tools">
                  {t('hero.cta')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/tools/image-compressor">
                  {t('hero.cta')}
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="px-6 py-12 bg-muted/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">{t('features.title')}</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                      <feature.icon className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Tools Categories Section */}
        <section id="tools" className="px-6 py-12">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">{t('categories.title')}</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(toolCategories).map(([categoryKey, category]) => {
                const Icon = categoryIcons[categoryKey as keyof typeof categoryIcons];
                const categoryTools = getToolsByCategory(categoryKey);

                return (
                  <Card key={categoryKey} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{tNav(`categories.${categoryKey}`)}</CardTitle>
                          <CardDescription>{category.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {categoryTools.slice(0, 4).map((tool) => (
                          <Link
                            key={tool.id}
                            href={`/${locale}${tool.path}`}
                            className="block p-2 rounded hover:bg-accent text-sm transition-colors"
                          >
                            {tNav(`tools.${tool.id}`)}
                          </Link>
                        ))}
                        {categoryTools.length > 4 && (
                          <div className="text-sm text-muted-foreground pt-2">
                            {t('categories.viewAll')} ({categoryTools.length - 4})
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
