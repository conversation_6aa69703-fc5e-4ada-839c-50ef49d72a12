"use client";

import { useState, useCallback } from "react";
import { Upload, Download, Scissors, FileText, Info } from "lucide-react";
import { PDFDocument } from "pdf-lib";
import { TopNavigation } from "@/components/TopNavigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";

interface SplitResult {
  name: string;
  url: string;
  pageRange: string;
  size: number;
}

export default function PdfSplitterClient() {
  const t = useTranslations('tools.pdfSplitter');
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [pageCount, setPageCount] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [splitResults, setSplitResults] = useState<SplitResult[]>([]);
  const [splitMode, setSplitMode] = useState<'pages' | 'range'>('pages');
  const [pagesPerFile, setPagesPerFile] = useState<number>(1);
  const [customRanges, setCustomRanges] = useState<string>('');

  const handleFileSelect = useCallback(async (files: FileList) => {
    const file = files[0];
    
    if (!file || file.type !== 'application/pdf') {
      alert('Please select a valid PDF file');
      return;
    }

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfDoc = await PDFDocument.load(arrayBuffer);
      const pages = pdfDoc.getPageCount();
      
      setPdfFile(file);
      setPageCount(pages);
      setSplitResults([]);
    } catch (error) {
      console.error('Failed to read PDF file:', error);
      alert('Unable to read PDF file, please ensure the file is valid');
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const parseRanges = (rangeString: string): Array<{start: number, end: number}> => {
    const ranges: Array<{start: number, end: number}> = [];
    const parts = rangeString.split(',').map(s => s.trim());
    
    for (const part of parts) {
      if (part.includes('-')) {
        const [start, end] = part.split('-').map(s => parseInt(s.trim()));
        if (start && end && start <= end && start >= 1 && end <= pageCount) {
          ranges.push({ start, end });
        }
      } else {
        const page = parseInt(part);
        if (page >= 1 && page <= pageCount) {
          ranges.push({ start: page, end: page });
        }
      }
    }
    
    return ranges;
  };

  const splitPDF = async () => {
    if (!pdfFile) return;

    setIsProcessing(true);
    setSplitResults([]);

    try {
      const arrayBuffer = await pdfFile.arrayBuffer();
      const originalPdf = await PDFDocument.load(arrayBuffer);
      const results: SplitResult[] = [];

      if (splitMode === 'pages') {
        // Split by page count
        const totalFiles = Math.ceil(pageCount / pagesPerFile);
        
        for (let i = 0; i < totalFiles; i++) {
          const startPage = i * pagesPerFile;
          const endPage = Math.min(startPage + pagesPerFile - 1, pageCount - 1);
          
          const newPdf = await PDFDocument.create();
          const pageIndices = Array.from(
            { length: endPage - startPage + 1 }, 
            (_, index) => startPage + index
          );
          
          const copiedPages = await newPdf.copyPages(originalPdf, pageIndices);
          copiedPages.forEach(page => newPdf.addPage(page));
          
          const pdfBytes = await newPdf.save();
          const blob = new Blob([pdfBytes], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);
          
          results.push({
            name: `${pdfFile.name.replace('.pdf', '')}_part${i + 1}.pdf`,
            url,
            pageRange: `Pages ${startPage + 1}-${endPage + 1}`,
            size: pdfBytes.length,
          });
        }
      } else {
        // Split by custom ranges
        const ranges = parseRanges(customRanges);
        
        for (let i = 0; i < ranges.length; i++) {
          const { start, end } = ranges[i];
          const newPdf = await PDFDocument.create();
          const pageIndices = Array.from(
            { length: end - start + 1 }, 
            (_, index) => start - 1 + index
          );
          
          const copiedPages = await newPdf.copyPages(originalPdf, pageIndices);
          copiedPages.forEach(page => newPdf.addPage(page));
          
          const pdfBytes = await newPdf.save();
          const blob = new Blob([pdfBytes], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);
          
          results.push({
            name: `${pdfFile.name.replace('.pdf', '')}_pages${start}-${end}.pdf`,
            url,
            pageRange: start === end ? `Page ${start}` : `Pages ${start}-${end}`,
            size: pdfBytes.length,
          });
        }
      }

      setSplitResults(results);
    } catch (error) {
      console.error('Failed to split PDF:', error);
      alert('Error occurred while splitting PDF, please check settings and file');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadFile = (result: SplitResult) => {
    const link = document.createElement('a');
    link.href = result.url;
    link.download = result.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadAll = () => {
    splitResults.forEach((result, index) => {
      setTimeout(() => downloadFile(result), index * 100);
    });
  };

  const clearAll = () => {
    setPdfFile(null);
    setPageCount(0);
    splitResults.forEach(result => URL.revokeObjectURL(result.url));
    setSplitResults([]);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-gray-600">
              {t('subtitle')}
            </p>
          </div>

          {/* Upload Area */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">{t('upload.title')}</h3>
                <p className="text-gray-500 mb-4">
                  {t('upload.subtitle')}
                </p>
                <Button asChild>
                  <label>
                    {t('upload.button')}
                    <input
                      type="file"
                      accept=".pdf,application/pdf"
                      className="hidden"
                      onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                    />
                  </label>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* File Info */}
          {pdfFile && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t('fileInfo.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">{t('fileInfo.filename')}</p>
                    <p className="font-medium">{pdfFile.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{t('fileInfo.filesize')}</p>
                    <p className="font-medium">{formatFileSize(pdfFile.size)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{t('fileInfo.totalPages')}</p>
                    <p className="font-medium">{pageCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Split Settings */}
          {pdfFile && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Scissors className="h-5 w-5" />
                  {t('settings.title')}
                </CardTitle>
                <CardDescription>{t('settings.description')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">{t('settings.mode')}</label>
                  <div className="flex gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        value="pages"
                        checked={splitMode === 'pages'}
                        onChange={(e) => setSplitMode(e.target.value as 'pages')}
                      />
                      {t('settings.modes.pages')}
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        value="range"
                        checked={splitMode === 'range'}
                        onChange={(e) => setSplitMode(e.target.value as 'range')}
                      />
                      {t('settings.modes.range')}
                    </label>
                  </div>
                </div>

                {splitMode === 'pages' ? (
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      {t('settings.pagesPerFile')}
                    </label>
                    <Input
                      type="number"
                      min="1"
                      max={pageCount}
                      value={pagesPerFile}
                      onChange={(e) => setPagesPerFile(parseInt(e.target.value) || 1)}
                      className="w-32"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {t('settings.willGenerate', { count: Math.ceil(pageCount / pagesPerFile) })}
                    </p>
                  </div>
                ) : (
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      {t('settings.pageRange')}
                    </label>
                    <Input
                      type="text"
                      value={customRanges}
                      onChange={(e) => setCustomRanges(e.target.value)}
                      placeholder={t('settings.pageRangePlaceholder')}
                      className="w-full"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {t('settings.pageRangeDescription')}
                    </p>
                  </div>
                )}

                <Button
                  onClick={splitPDF}
                  disabled={isProcessing || (splitMode === 'range' && !customRanges.trim())}
                  className="w-full"
                >
                  {isProcessing ? t('settings.splitting') : t('settings.split')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Processing Status */}
          {isProcessing && (
            <Card className="mb-6">
              <CardContent className="p-6 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>{t('processing.title')}</p>
              </CardContent>
            </Card>
          )}

          {/* Results */}
          {splitResults.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Download className="h-5 w-5" />
                      {t('result.title')}
                    </CardTitle>
                    <CardDescription>{t('result.description')}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={downloadAll}>
                      {t('result.downloadAll')}
                    </Button>
                    <Button onClick={clearAll} variant="outline">
                      {t('result.clear')}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {splitResults.map((result, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 border rounded-lg bg-white">
                      <div className="flex-1">
                        <h4 className="font-medium">{result.name}</h4>
                        <div className="text-sm text-gray-500">
                          {result.pageRange} • {formatFileSize(result.size)}
                        </div>
                      </div>
                      <Button onClick={() => downloadFile(result)} size="sm">
                        {t('result.download')}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                {t('info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                {t.raw('info.items').map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
