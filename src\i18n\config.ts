export const locales = ['zh', 'en', 'ja', 'de', 'ru'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'zh';

export const localeNames: Record<Locale, string> = {
  zh: '中文',
  en: 'English',
  ja: '日本語',
  de: 'Deutsch',
  ru: 'Русский',
};

export const localeLabels: Record<Locale, string> = {
  zh: '🇨🇳 中文',
  en: '🇺🇸 English',
  ja: '🇯🇵 日本語',
  de: '🇩🇪 Deutsch',
  ru: '🇷🇺 Русский',
};
